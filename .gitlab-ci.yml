################################################################################
####    VARIABLES
################################################################################
variables:
  LC_ALL: C.UTF-8
  LANG: C.UTF-8
  REGION: europe-west1

stages:
  - deploy
  - promote
  - crontab

image: google/cloud-sdk:slim

before_script:
  - echo $CI_ENVIRONMENT_NAME
  - echo $PROJECT_ID
  - gcloud config set project $PROJECT_ID
  - cd $CI_PROJECT_DIR/

#-----------------------------------------------------------------
#   TEMPLATES
#-----------------------------------------------------------------

## PREPROD TAGS
.preprod_tags: &preprod_tags
  environment: PREPROD
  tags:
    - GCP
    - DEPLOY
    - IT-DATA
    - PREPROD
  only:
    - develop
    #- feature/ITDATA-5853__total_energie_api

## PROD TAGS
.prod_tags: &prod_tags
  environment: PROD
  tags:
    - GCP
    - DEPLOY
    - IT-DATA
    - PROD
  only:
    - master

.deploy: &deploy
  stage: deploy
  script:
    - export VERSION="$(date +'%Y%m%dt%H%M%S')-$CI_COMMIT_SHORT_SHA"
    - echo $VERSION
    - gcloud app deploy app-$env.yaml  --project=$PROJECT_ID --quiet --no-promote --version=${VERSION} #--verbosity=debug --log-http

.promote: &promote
  stage: promote
  script:
    - VERSION="$(gcloud app versions list --project=$PROJECT_ID | grep "$CI_COMMIT_SHORT_SHA" | awk '{print $2}')"
    - echo $VERSION
    - gcloud app services set-traffic default --splits ${VERSION}=1  --project=$PROJECT_ID --quiet
  when:
    manual

.crontab: &crontab
  stage: crontab
  script:
    - gcloud app deploy cron.yaml --project=$PROJECT_ID --quiet
  when:
    manual

#-----------------------------------------------------------------
#   DEPLOY SERVICE = DEFAULT
#-----------------------------------------------------------------
🚀:deploy:preprod:
  variables:
    env: preprod
  <<: *preprod_tags
  <<: *deploy

🚀:deploy:prod:
  variables:
    env: prod
  <<: *prod_tags
  <<: *deploy

#-----------------------------------------------------------------
#   PROMOTE / SPLIT TRAFFIC SERVICE = DEFAULT
#-----------------------------------------------------------------
✅:promote:preprod:
  <<: *preprod_tags
  <<: *promote
  needs: ["🚀:deploy:preprod"]

✅:promote:prod:
  <<: *prod_tags
  <<: *promote
  needs: ["🚀:deploy:prod"]

#-----------------------------------------------------------------
#   CRON
#-----------------------------------------------------------------
🤖:deploy_cron:preprod:
  <<: *preprod_tags
  <<: *crontab

🤖:deploy_cron:prod:
  <<: *prod_tags
  <<: *crontab
