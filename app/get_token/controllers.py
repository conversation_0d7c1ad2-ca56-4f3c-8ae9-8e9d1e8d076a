import os

import requests
from flask import Blueprint, abort, request
import app.__gcp_commun_services.secret_manager as secret

firebase_api_key = secret.get_secret(os.environ["PROJECT_ID"], os.environ["FIREBASE_API_KEY_SECRET"])

auth_url = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={api_key}".format(
    api_key=firebase_api_key)
refresh_token_url = "https://securetoken.googleapis.com/v1/token?key={api_key}".format(api_key=firebase_api_key)

auth_blueprint = Blueprint('auth', __name__, url_prefix='/auth')


@auth_blueprint.route('/', methods=['POST'])
def main():
    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return abort(401, f'no input provided')
    if request_json:
        if 'email' in request_json:
            if 'password' not in request_json:
                return abort(401, f'password argument required')
            email = request_json['email']
            password = request_json['password']
            data = generate_token(email, password)
            return data, 200
        elif 'refresh' in request_json:
            token = request_json['refresh']
            data = refresh_token(token)
            return data, 200
        else:
            return f'no valid input set'

    else:
        return f'no valid input set'


def generate_token(username, password):
    try:
        payload = {
            "email": username,
            "password": password,
            "returnSecureToken": "true"
        }
        get_token = requests.post(auth_url, json=payload)
        if get_token.ok is True:
            token = get_token.json()
            token_jwt = token['idToken']
            refresh_token_value = token['refreshToken']
            expire_in = token['expiresIn']

            return {'token': token_jwt, "refreshToken": refresh_token_value, "expireIn": expire_in}
        return get_token.json()
    except Exception as e:
        return abort(401, f'Invalid key:{e}')


def refresh_token(token):
    try:
        payload = {
            "grant_type": "refresh_token",
            "refresh_token": token
        }
        get_token = requests.post(refresh_token_url, json=payload)
        if get_token.ok is True:
            token = get_token.json()
            token_jwt = token['id_token']
            refresh_token_value = token['refresh_token']
            expire_in = token['expires_in']

            return {'token': token_jwt, "refreshToken": refresh_token_value, "expireIn": expire_in}
        return get_token.json()
    except Exception as e:
        return abort(401, f'Invalid key:{e}')
