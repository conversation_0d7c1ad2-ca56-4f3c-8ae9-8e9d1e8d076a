# Import flask dependencies
import json

import requests as req
from flask import Blueprint
from flask import request

header_json = {'content-type': 'application/json'}
remote_url = 'https://audika-pro.fr/api/contact/insert'

audika_media_code = {
    845153: 'AFNEOPRI1',
    698425: 'AFNEOPRI1',
}
qualifio_game_ids = list(audika_media_code.keys())

audika_headers = {
    'Content-Type': 'application/json',
    'X-Api-Key': '094dcb9443f58eb5eaf04d518feb6864'
}

qualifio_to_audika_blueprint = Blueprint('to_audika', __name__, url_prefix='/qualifio-to-audika')


@qualifio_to_audika_blueprint.route('/', methods=['GET', 'POST', 'PUT'])
def qualifio_data_to_audika():
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    print('Qualifio request : {}'.format(json.dumps(request_json)))

    # ignore unknown game id
    if int(request_json['game']['id']) not in qualifio_game_ids:
        print('Ignore game id : {}'.format(str(request_json['game']['id'])))
        return json.dumps({'msg': 'game id ignored'}), 204, header_json

    # no crm info
    if request_json.get('crm') is None:
        print('No CRM data')
        return json.dumps({'error': 'no crm data'}), 442, header_json
    # Transform : qualifio_to_audika -> audika
    data = {
        'advertiser': 'PrismaMedia',
        'type': 'callback',
        'pageMediaCode': audika_media_code.get(int(request_json['game']['id'])),
        'lastname': request_json['crm'].get('lastname', ''),
        'firstname': request_json['crm'].get('firstname', ''),
        'email': request_json['crm'].get('email', ''),
        'tel': request_json['crm'].get('phone', ''),
    }

    print('Send data to Audika : {}'.format(json.dumps(data)))

    result = req.post(remote_url, data=json.dumps(data), headers=audika_headers)
    if result.status_code != req.codes.ok:
        print('Audika has rejected our data. status_code = {}'.format(str(result.status_code)))
        print('Response body : {}'.format(result.content))
        return json.dumps({'error': 'remote api failed'}), 542, header_json

    print('Audika has accepted our data')
    print('Response body : {}'.format(result.content))
    return json.dumps({'status': 'ok'}), 200, header_json
