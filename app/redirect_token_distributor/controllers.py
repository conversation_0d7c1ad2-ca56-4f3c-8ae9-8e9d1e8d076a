# Import flask dependencies
import logging
import os
import re
from hashlib import sha256

import sqlalchemy
from flask import Blueprint, request, redirect

import app.__gcp_commun_services.cloudsql as cloudsql

logging.basicConfig()  # log messages to stdout
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(logging.INFO)

db_username = 'esampling_app'
db_engine = cloudsql.init_db_engine(db_username)

import secret_manager as secret

pmc_secret = secret.get_secret(os.environ["PROJECT_ID"], os.environ["PMC_EMAIL_TOKEN_SECRET"])
wr_secret = secret.get_secret(os.environ["PROJECT_ID"], os.environ["WEBRIVAGE_EMAIL_TOKEN_SECRET"])


def __is_valid_access(request, email_sha256):
    ## redirect-link is protected with pmc_email_token
    req_token = request.args.get('t', '').strip().lower()
    if not re.match(r'^[a-z0-9]{64}$', req_token):
        return False

    # Prisma date format is YYYYmmdd
    # Webrivage date format is YYYY-mm-dd
    req_date = request.args.get('d', '').strip().lower()
    re_match = re.match(r'^(?P<year>20[2-3][0-9])(?P<month>0[0-9]|1[0-2])(?P<day>[0-2][0-9]|3[0-1])$', req_date)
    if re_match:
        # we have prisma media date, build req_date correctly
        req_date = re_match['year'] + '-' + re_match['month'] + '-' + re_match['day']
    else:
        # check webrivage date format
        re_match = re.match(r'^(?P<year>20[2-3][0-9])-(?P<month>0[0-9]|1[0-2])-(?P<day>[0-2][0-9]|3[0-1])$', req_date)
        if re_match is None:
            return False

    ## check token against PrismaConnect (pmc) and webrivage (wr) wanted token
    if req_token == sha256((email_sha256 + pmc_secret + req_date).encode('utf8')).hexdigest():
        return True
    if req_token == sha256((email_sha256 + wr_secret + req_date).encode('utf8')).hexdigest():
        return True

    return False


distrib_blueprint = Blueprint('distrib_token', __name__, url_prefix='/distrib')


# Set the route and accepted methods
@distrib_blueprint.route('/', defaults={'container': '', 'email_sha256': ''}, methods=['GET'])
@distrib_blueprint.route('/<string:container>/<string:email_sha256>', methods=['GET'])
def get_token_url(container: str, email_sha256: str):
    container = container.strip().lower()
    email_sha256 = email_sha256.strip().lower()

    if container == '' or email_sha256 == '':
        return 'Cannot process this empty request', 400

    if len(email_sha256) != 64:
        return 'Cannot process this invalid request', 412

    if not __is_valid_access(request, email_sha256):
        return 'Unauthorized access', 401

    # 1. check : does the container exist ?
    try:
        table_name = "token_distrib_container_" + container
        does_table_exist = cloudsql.table_exists(db_engine, table_name, "esampling")
        if not does_table_exist:
            logging.info('cannot find container "{}"'.format(container))
            return 'cannot find container', 403
    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return 'Cannot process your request table exists. Cannot connect to DB!!', 550

    # 2. try to retrieve <token_url> for <email_sha256>
    token_url = None
    nb_hit = 1
    try:
        with db_engine.connect() as conn:
            query1 = f"SELECT token_url FROM esampling.{table_name} WHERE email_sha256 = '{email_sha256}'"
            stmt1 = sqlalchemy.text(query1)

            result = (conn.execute(stmt1)).first()
            if result:
                # 3. We get the already assigned token_url :). Update last_seen_date and nb_hit
                token_url = result.token_url
                query2 = f"""
                    UPDATE esampling.{table_name}
                    SET nb_hit = nb_hit + 1,
                        last_seen_date = now()
                    WHERE email_sha256 = '{email_sha256}'
                    RETURNING nb_hit
                """

                stmt2 = sqlalchemy.text(query2)
                result = (conn.execute(stmt2)).first()
                nb_hit = result.nb_hit
            else:
                # 4. try to get token_url 3 times.
                tries = 0
                while not token_url:
                    tries += 1
                    # see https://dba.stackexchange.com/questions/69471/postgres-update-limit-1/69497#69497
                    query3 = f"""
                        UPDATE esampling.{table_name}
                        SET email_sha256 = '{email_sha256}',
                            nb_hit = 1,
                            assign_date = now(),
                            last_seen_date = now()
                        WHERE token_url = (
                            SELECT token_url
                            FROM   esampling.{table_name}
                            WHERE  email_sha256 IS NULL
                            LIMIT  1
                            FOR UPDATE SKIP LOCKED
                        )
                        RETURNING token_url
                    """

                    stmt3 = sqlalchemy.text(query3)
                    result = (conn.execute(stmt3)).first()
                    if result:
                        token_url = result.token_url
                        nb_hit = 1
                    if tries == 3:
                        # error
                        return 'Cannot retrieve destination url!', 501

    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return 'Cannot process your request. Cannot connect to DB!', 551

    # redirect user to token_url
    response = redirect(token_url, code=302)
    response.headers['nb_hit'] = nb_hit

    return response
