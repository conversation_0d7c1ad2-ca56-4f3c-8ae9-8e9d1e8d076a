# Import flask dependencies
import json
import logging
import os

import requests as req
from flask import Blueprint, request

api_url = 'https://rogue-one.prismadata.fr/api/v1/tracking'

header_json = {'content-type': 'application/json'}
env = os.getenv("ENV")
if env != 'prod':
    api_url = 'https://rogue-one.preprod.prismadata.fr/api/v1/tracking'

headers = {
    'Content-Type': 'application/json'
}

rogue_one_track_blueprint = Blueprint('rogue_one_track', __name__, url_prefix='/rogue-one-tracking')


@rogue_one_track_blueprint.route('/', methods=['POST'])
def rogue_one_tracking_main():
    """
    HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return json.dumps({'error': 'Invalid request'}), 400, header_json

    # get x_auth_token form query params
    x_auth_token = request.args.get('x_auth_token', None)
    if request.headers.get('X-Auth-Token', x_auth_token):
        # inject x-auth-token in headers that should be sent to rogue-one
        headers["X-Auth-Token"] = request.headers.get('X-Auth-Token', x_auth_token)

    # debug mode
    is_debug_mode = False
    if request.headers.get('X-Debug-Prisma-Api', None):
        is_debug_mode = True
        headers['X-Debug-Prisma-Api'] = request.headers.get('X-debug-prisma-api', None)

    # prepare data to send to rogue-one
    data = {
        'input-html': request_json.get('input-html', ''),
        'client': request_json['client'],
        'public-ref': request_json['public-ref']
    }

    logging.info(f"sending data to Rogue One: {data}")

    try:
        # send data to Rogue-One
        result = req.post(api_url, data=json.dumps(data), headers=headers)
        result.raise_for_status()  # Raise an exception for HTTP errors

        result_json = result.json()

        # if debug and rogue-one in debug mode too
        if is_debug_mode and result_json.get('more', {}).get('debug', None):
            result_json['more']['debug']['r1-response-from-proxy-time'] = str(int(result.elapsed.total_seconds() * 1000)) + 'ms'

        # return api result
        return result_json, result.status_code
    except req.exceptions.RequestException as e:
        logging.error(f"Error sending to Rogue One: {e}")
        return {f"Error sending to Rogue One: {e}"}, e.response.status_code if e.response else 500
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding Rogue One response: {e}")
        return {"error": "Invalid JSON response"}, result.status_code
