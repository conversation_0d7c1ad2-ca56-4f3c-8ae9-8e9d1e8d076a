import json
import logging
import os
from functools import wraps

from firebase_admin import auth
from flask import Blueprint, jsonify, make_response, request

import app.__gcp_commun_services.secret_manager as secret_manager
import app.__services.data_direct as data_direct
import app.__services.tmail as tmail
import app.__services.karinto as karinto
import app.__services.utils as utils

header_json = {'content-type': 'application/json'}
env = os.getenv("ENV")

headers = {
    'Content-Type': 'application/json'
}

notification_blueprint = Blueprint('notification', __name__, url_prefix='/notification')


def authenticated(fn):
    @wraps(fn)
    def wrapped():
        try:
            # Extract the firebase token from the HTTP header
            token = request.headers['Authorization']
            token = token.replace('Bearer ', '')
            # Validate the token
            verified = auth.verify_id_token(token)
        except Exception as e:
            # If an exception occurred above, reject the request
            msg = 'Invalid Credentials:{}'.format(e)
            resp = make_response(jsonify(message=msg), 401)
            return resp
        # Execute the authenticated function
        return fn(request)
        # Return the input function "wrapped" with our
        # authentication check, i.e. fn(authenticated(request))

    return wrapped

dd_username = 'notification'

@notification_blueprint.route('/', methods=['POST'])
@authenticated
def notification_main(request):
    """
    HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return json.dumps({'error': 'Invalid request'}), 400, header_json

    mandatory_params = ["brand_trigram", "consent_public_ref", "recipient", "template_name", "vars"]

    validated_consents = {
        "TEL": [
            "tele_loisirs_comment_notif_alert",
            "tele_loisirs_like_notif_alert"
        ]
    }

    # 1- check required params (consents, brand trigram ...)
    missing_params = []
    for param in mandatory_params:
        if param not in request_json:
            missing_params.append(param)

    if missing_params:
        return json.dumps({'error': f'Missing parameters: {", ".join(missing_params)}'}), 400, header_json

    brand_trigram = request_json.get('brand_trigram')
    if brand_trigram not in validated_consents.keys():
        return json.dumps({'error': 'Invalid brand trigram value !!'}), 400, header_json

    consent_public_ref = request_json.get('consent_public_ref')

    if consent_public_ref not in validated_consents.get(brand_trigram):
        return json.dumps({'error': 'Invalid consent public ref value !!'}), 400, header_json

    email = request_json.get('recipient')

    # 2- check consent status for profile using data direct api
    logging.info('Calling Data Direct API..')

    dd_result = data_direct.list_consents(dd_username, email)

    print('st code : ' + str(dd_result.status_code))

    if dd_result is None:
        dd_result = {}
        result_json = {}
    else:
        if isinstance(dd_result, tuple):
            result_json = dd_result
        else:
            result_json = json.loads(dd_result.text)

    logging.info(' ----> DD list consents result_json: {}'.format(result_json))
    print(' ----> DD list consents result_json: {}'.format(result_json))

    logging.info(dd_result.status_code)

    if dd_result.status_code != 200 or (result_json.get('code') and result_json.get('code') != 200):
        # api error, return result
        return result_json, dd_result.status_code

    consents = result_json.get('consents')
    if not consents:
        return json.dumps({'error': f'No consent found for context {dd_username}!!'}), 403, header_json

    is_sub = None
    for consent in consents:
        if consent["public_ref"] == consent_public_ref:
            is_sub = bool(consent["sub"])
            break # Exit the loop

    logging.info(f'Email: {email} is sub to consent {consent_public_ref} ? = {is_sub}')
    print(f'Email {email} is sub to consent {consent_public_ref} ? = {is_sub}')

    if not is_sub or is_sub is None:
        return json.dumps({'error': f'Email ({email}) not subscribed to the provided consent = {consent_public_ref} value'}), 422, header_json

    # email sub everything is ok -> call tmail api
    brand = request_json.get('brand_trigram').lower()

    tmail_api_secret_name = 'itdata_tmail_api-{brand}'.format(brand=brand)
    tmail_secret = secret_manager.get_secret(os.environ["SAFE_PROJECT_ID"], tmail_api_secret_name)

    sha256 = utils.__get_sha256(email)

    try:
        consent_infos = karinto.get_consent_infos(consent_public_ref, 'unsubscribe_domain')
    except Exception as e:
        return json.dumps({'DB EXCEPTION': str(e)}), 500, header_json

    unsubscribe_domain = consent_infos.get('unsubscribe_domain')

    unsub_url = f'https://{unsubscribe_domain}/a{sha256}/b{sha256}'

    default_params = {
        'type': 'template',
        'tracking_key': 'notification_alerte',
    }
    request_json.update(default_params)
    auto_vars = {
        'unsub_url' : unsub_url,
    }
    request_json['vars'].update(auto_vars)

    logging.info('Calling Tmail API..')

    # 3- send transaction email with tmail api
    return tmail.send_email_transactional(request_json, brand, tmail_secret)
