import json
import logging

import sqlalchemy

import app.__gcp_commun_services.cloudsql as cloudsql

logging.basicConfig()  # log messages to stdout
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(logging.INFO)

header_json = {'content-type': 'application/json'}
db_username = 'karinto_app'


def get_consent_infos(public_ref, fields='*'):
    """
    Return consent information or False if not found for the given public_ref!
    :public_ref string
    :fields string or list of filed to select from table
    :return dict or False
    """
    if isinstance(fields, (list, tuple)):
        fields_str = ', '.join(map(str, fields))  # Use map to handle non-string elements
    else:
        fields_str = str(fields)  # Ensure it's always a string

    db_engine = cloudsql.init_db_engine(db_username)

    if not db_engine:
        return json.dumps({'error': 'DB Engine Error'}), 500, header_json

    query = f"""
            SELECT {fields_str} FROM karinto.email_consent where public_ref = '{public_ref}'
        """

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            consent_entity = (conn.execute(stmt)).first()
            if consent_entity:
                logging.info('consent data : {}'.format(str(consent_entity._asdict())))
                return consent_entity._asdict()

            print('Not found consent for the given public_ref')
            logging.info(f'Not found consent for the given public_ref= {public_ref}')
            return False

    except Exception as e:
        raise Exception(f'DB Exception: {e}')
