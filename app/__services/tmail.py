import datetime as dt
import json
import logging
import os
from hashlib import sha256
from urllib.parse import urlparse

import requests as req

headers = {
    'Content-Type': 'application/json'
}

tmail_host = os.getenv('TMAIL_HOST', 'https://tmail.preprod.prismadata.fr').rstrip("/")
tmail_api_email_transactional_url = '{host}/api/email'.format(host=tmail_host)
tmail_api_alert_to_list_url = '{host}/api/api/alert-to-list'.format(host=tmail_host)

env = os.getenv("ENV")


def get_url_path(url: str):
    parsed = urlparse(url)
    return parsed.path


def generate_token(url: str, method: str, content: str, secret: str):
    now = dt.datetime.now(dt.timezone.utc).isoformat()
    route = get_url_path(url)
    # concat string to hash
    to_hash = '|'.join(['', now[0:17] + '00', method.upper(), route, secret, content, ''])
    logging.info(to_hash)
    # generate token
    return sha256(to_hash.encode('utf-8')).hexdigest()


def send_email_transactional(request_json, brand, secret):
    token = generate_token(tmail_api_email_transactional_url, 'post', json.dumps(request_json), secret)
    headers["X-Auth-Token"] = token
    headers["X-Auth-Identity"] = brand
    # send data to Tmail
    logging.info('Calling Tmail API..')

    # 4- send transaction email with tmail api
    result = req.post(tmail_api_email_transactional_url, data=json.dumps(request_json), headers=headers)
    result_json = result.json()
    logging.info(result_json)

    return result_json, result.status_code

