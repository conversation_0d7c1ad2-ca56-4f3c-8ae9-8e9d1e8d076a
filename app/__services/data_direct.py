import hashlib
import logging
import os
import time

import requests as req

import app.__gcp_commun_services.secret_manager as secret_manager
import app.__services.utils as utils
from app.__gcp_commun_services.firestore import FirestoreManager

header_json = {'content-type': 'application/json'}
data_direct_host = os.getenv('DD_HOST', 'https://data-direct.preprod.prismadata.fr').rstrip("/")


def generate_data_direct_auth_token(username):
    """
    Generates a new Data Direct access token using the stored credentials.

    Returns:
        dict: The JSON response from the Data Direct API if successful, otherwise False.
    """
    url = f'{data_direct_host}/auth/token'
    secret_id = f'itdata_datadirect_api-{username}'
    password = secret_manager.get_secret(os.environ["SAFE_PROJECT_ID"], secret_id)

    data = {
        "username": username,
        "password": password,
    }

    try:
        response = req.post(url, headers={'Content-Type': 'application/json'}, json=data)

        if response.status_code == 200:
            try:
                response_json = response.json()
                return response_json  # Token generated successfully
            except ValueError as e:
                logging.error(f"DD API generate token Value Error: Invalid JSON response: {e}")
                return False
        else:
            logging.error(f"DD API generate token Error: Request failed with status code {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"DD API generate token Error: exception: {e}")
        return False


def refresh_data_direct_token(refresh_token):
    """
    Refreshes the Data Direct access token using the provided refresh token.

    Args:
        refresh_token: The refresh token to use for refreshing the access token.

    Returns:
        dict: The JSON response from the Data Direct API if successful, otherwise False.
    """
    url = f'{data_direct_host}/auth/refresh-token'
    body = {"refresh_token": f"{refresh_token}"}

    try:
        response = req.post(url, json=body)

        if response.status_code == 200:
            try:
                response_json = response.json()
                return response_json  # Token refreshed successfully
            except ValueError as e:
                logging.error(f"DD API Error: Invalid JSON response: {e}")
                return False
        else:
            logging.error(f"DD API Error: Refresh token request failed with status code: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"DD API Error: Error refreshing token: {e}")
        return False


class TokenManager:
    """
    Generate or Refresh Data Direct Api token
    """
    def __init__(self):
        firestore_client = FirestoreManager()
        # Reference to the token document
        self.doc_ref = firestore_client.get_document('data_direct', f'dd_token')
        # Get existing data or initialize empty
        self.token_data = self.doc_ref.get().to_dict() or {}  # Get existing data or initialize empty

        self.expire_in = self.token_data.get('expire_in', 7200)
        self.access_token = self.token_data.get('access_token')
        self.refresh_token = self.token_data.get('refresh_token')
        self.expiration_time = self.token_data.get('expiration_time')
        self.dd_username = None

    def _update_token_document(self):
        """Updates the Firestore document with the current token data."""
        self.doc_ref.set({
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'expiration_time': self.expiration_time,
            'expire_in': self.expire_in
        })

    def get_access_token(self):
        if not self.refresh_token or not self.access_token or self.is_token_expired():
            logging.info('Generating new Data Direct token...')
            self._get_new_token()
            logging.info('New Data Direct token generated successfully.')
        logging.info('Access token retrieved successfully.')
        return self.access_token

    def _get_new_token(self):
        """Handles both initial token generation and refreshing."""
        if self.refresh_token:
            logging.info('Refreshing Data Direct token...')
            response_json = refresh_data_direct_token(self.refresh_token)
            if not response_json:
                logging.error("Failed to refresh Data Direct token. Let's Try to generate a new one...")
                response_json = generate_data_direct_auth_token(self.dd_username)
        else:
            logging.info('Generating new Data Direct token using username {}'.format(self.dd_username))
            response_json = generate_data_direct_auth_token(self.dd_username)

        if response_json:  # Check if token generation/refresh was successful
            logging.info('Data Direct token generated/refreshed successfully.')
            self.access_token = response_json.get('access_token', '')
            self.refresh_token = response_json.get('refresh_token', '')
            self.expire_in = response_json.get('expire_in', 7200)
            self.expiration_time = time.time() + self.expire_in
            self._update_token_document()  # Update Firestore
        else:
            logging.error("Failed to generate or refresh Data Direct token.")
            return {"error": "Failed to obtain Data Direct access token"}, 500, header_json

    def is_token_expired(self):
        logging.info(f'Checking if Data Direct token is expired...')
        logging.info(f'Expiration time: {self.expiration_time}, Current time: {time.time()}')
        logging.info(f'Is expired: {self.expiration_time and time.time() >= self.expiration_time}')

        return self.expiration_time and time.time() >= self.expiration_time


def check_service(username, service, brand_trigram, email):
    data_direct_api_check_service = '{host}/api/notify-me/profile/{email_sha256}/service_status/{service}/brand/{brand}/is_eligible'

    # 1- get data direct access token
    token = TokenManager()
    token.dd_username = username
    access_token = token.get_access_token()

    if not access_token:
        return {"error": "DD API Access Token Error: Empty Access Token"}, 400, header_json

    data_direct_api_uri = data_direct_api_check_service.format(
        host=data_direct_host,
        email_sha256=hashlib.sha256(email.strip().lower().encode()).hexdigest(),
        service=service,
        brand=brand_trigram
    )

    logging.info(f'Calling Data Direct API..{data_direct_api_uri}')

    dd_headers = {
        'Content-Type': 'application/json',
        "authorization": f"Bearer {access_token}"
    }

    # 2- check eligible service for profile using data direct api
    return req.get(url=data_direct_api_uri, headers=dd_headers)


def list_consents(username, email):
    email_sha256 = utils.__get_sha256(email)
    data_direct_api_uri = f'{data_direct_host}/api/profile/{email_sha256}/email_status/list'

    # 1- get data direct access token
    token = TokenManager()
    token.dd_username = username
    access_token = token.get_access_token()

    if not access_token:
        return {"error": "DD API Access Token Error: Empty Access Token"}, 400, header_json

    logging.info(f'Calling Data Direct API..{data_direct_api_uri}')

    dd_headers = {
        'Content-Type': 'application/json',
        "authorization": f"Bearer {access_token}"
    }

    return req.get(url=data_direct_api_uri, headers=dd_headers)
