import datetime as dt
import json
import logging
import os
from functools import wraps
from hashlib import sha256
from urllib.parse import urlparse

import requests as req
from firebase_admin import auth
from flask import Blueprint, jsonify, make_response, request

import app.__gcp_commun_services.secret_manager as secret_manager
import app.__services.data_direct as data_direct
import app.__services.tmail as tmail

tmail_host = os.getenv('TMAIL_HOST', 'https://tmail.preprod.prismadata.fr').rstrip("/")
tmail_api_url = '{host}/api/email'.format(host=tmail_host)

header_json = {'content-type': 'application/json'}
env = os.getenv("ENV")

db_user = os.environ.get("DB_USERNAME_STOCKPILE", 'data_stockpile_app')

headers = {
    'Content-Type': 'application/json'
}

notify_me_blueprint = Blueprint('notify_me', __name__, url_prefix='/notify-me')

dd_username = 'notify_me'

def authenticated(fn):
    @wraps(fn)
    def wrapped():
        try:
            # Extract the firebase token from the HTTP header
            token = request.headers['Authorization']
            token = token.replace('Bearer ', '')
            # Validate the token
            verified = auth.verify_id_token(token)
        except Exception as e:
            # If an exception occured above, reject the request
            msg = 'Invalid Credentials:{}'.format(e)
            resp = make_response(jsonify(message=msg), 401)
            return resp
        # Execute the authenticated function
        return fn(request)
        # Return the input function "wrapped" with our
        # authentication check, i.e. fn(authenticated(request))

    return wrapped

@notify_me_blueprint.route('/', methods=['POST'])
@authenticated
def notify_me_main(request):
    """
    HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return json.dumps({'error': 'Invalid request'}), 400, header_json

    brand_trigram = request_json.get('brand_trigram', '')
    service = request_json.get('service', '')
    email = request_json.get('recipient', '')

    logging.info('Calling Data Direct API..')
    dd_result = data_direct.check_service(dd_username, service, brand_trigram, email)
    logging.info(dd_result)

    if dd_result is None:
        dd_result = {}
        result_json = {}
    else:
        result_json = json.loads(dd_result.text)

    logging.info('DD check service result_json: {}'.format(result_json))
    print('DD check service result_json: {}'.format(result_json))

    logging.info(dd_result.status_code)

    if dd_result.status_code != 200:
        # api error, return result
        return result_json, dd_result.status_code

    # everything is ok call tmail api
    brand = request_json.get('brand_trigram', 'prisma-connect').lower()

    tmail_secret_name = f'itdata_tmail_api-{brand}'
    secret = secret_manager.get_secret(os.environ["SAFE_PROJECT_ID"], tmail_secret_name)

    # 3- generate token for tmail api
    token = tmail.generate_token(tmail_api_url, 'post', json.dumps(request_json), secret)
    headers["X-Auth-Token"] = token
    headers["X-Auth-Identity"] = brand
    # send data to Tmail
    logging.info('Calling Tmail API..')

    # 4- send transaction email with tmail api
    result = req.post(tmail_api_url, data=json.dumps(request_json), headers=headers)
    result_json = result.json()
    logging.info(result_json)

    return result_json, result.status_code

