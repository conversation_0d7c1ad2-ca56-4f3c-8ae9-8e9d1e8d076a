import json
import flask
import requests as req

# disable FTM, while migrating to App<PERSON><PERSON>ine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}


def echo_request(request: flask.Request) -> object:
    """Responds to any HTTP request.
    Args:
        request (flask.Request): HTTP request object.
    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <http://flask.pocoo.org/docs/1.0/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json()
    print('Query String : ' + str(request.query_string))
    print('Headers : ' + str(request.headers))
    print('Json : ' + json.dumps(request_json))
    ret = {
        'status': 'ok',
        'query_string': str(request.query_string),
        'headers': str(request.headers),
        'json': request_json,
    }
    return json.dumps(ret), 200, header_json
