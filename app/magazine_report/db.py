# for BigQuery
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

# BigQuery client
client = bigquery.Client('pm-prod-matrix')


def create_table():
    dataset_ref = client.dataset('store')
    # Prepares a reference to the table
    table_ref = dataset_ref.table('abo_vno_report_email')
    try:
        client.get_table(table_ref)
    except NotFound:
        # if we don't find the table, create it
        schema = [
            bigquery.SchemaField('magazine_name', 'STRING', mode='REQUIRED',
                                 description='ref : store.report_abo_vno_email.magazine_name'),
            bigquery.SchemaField('release_number', 'INTEGER', mode='REQUIRED',
                                 description='ref : store.report_abo_vno_email.release_number'),
            bigquery.SchemaField('sent_date', 'DATE', mode='REQUIRED', description='Report email sent date'),
        ]
        table = bigquery.Table(table_ref, schema=schema)
        table = client.create_table(table)
        print('table {} created.'.format(table.table_id))


def insert_email_details(magazine_name, release_no, sent_date):
    table = client.get_table('store.abo_vno_report_email')  # API call

    rows_to_insert = [
        {u"magazine_name": magazine_name, u"release_number": release_no, u"sent_date": sent_date},
    ]
    errors = client.insert_rows(table, rows_to_insert)  # API request

    return errors


def get_reports(date: str):
    """
    Get the list of report emails to send

    Args:
        date (str) : current date or specific date used to fetch data

    Returns:
        (list) list of report with their general data
    """
    sql = """
        SELECT
            * EXCEPT (create_date)
        FROM `pm-prod-matrix.store.abo_vno_report_email_to_send`
        WHERE DATE(create_date) >= DATE_SUB(@date, INTERVAL 2 DAY)
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("date", "DATE", date)
        ]
    )

    query_result = client.query(sql, job_config=job_config)
    return query_result


def bq_get_report_sales_data(magazine_name: str, release_number: int):
    """
    Return report data, needed to build the table

    Args:
        magazine_name (str): magazine name
        release_number (int): release number

    Returns:
        specific report data fetched from BigQuery
    """
    sql = """
    SELECT * EXCEPT (report_email_release_magazine_name, report_email_release_number)
    FROM `pm-prod-matrix.store.abo_vno_report_sales_performance`
    WHERE report_email_release_magazine_name = @magazine_name
      AND report_email_release_number = @release_number
    ORDER BY metric_order ASC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("magazine_name", "STRING", magazine_name),
            bigquery.ScalarQueryParameter("release_number", "INT64", release_number),
        ]
    )

    query_result = client.query(sql, job_config=job_config)
    return query_result


def bq_get_report_budget_data(magazine_name: str, release_number: int):
    """
    Return report data, needed to build the table

    Args:
        magazine_name (str): magazine name
        release_number (int): release number

    Returns:
        specific report data fetched from BigQuery
    """
    sql = """
    SELECT * EXCEPT (report_email_release_magazine_name, report_email_release_number)
    FROM `pm-prod-matrix.store.abo_vno_report_budget_performance`
    WHERE report_email_release_magazine_name = @magazine_name
      AND report_email_release_number = @release_number
    ORDER BY metric_order ASC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("magazine_name", "STRING", magazine_name),
            bigquery.ScalarQueryParameter("release_number", "INT64", release_number),
        ]
    )

    query_result = client.query(sql, job_config=job_config)
    return query_result
