from flask import Blueprint, request
import json
import sys
import logging
import os
import app.__gcp_commun_services.secret_manager as secret
# from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib
import datetime as dt

import app.magazine_report.db as db
import app.magazine_report.template as template
import app.magazine_report.helper as helper

magazine_report_blueprint = Blueprint('magazine_report', __name__, url_prefix='/magazine-report')

header_json = {'content-type': 'application/json'}
global_x_prisma_auth = '5a82bdcf11c015103e6f2666d5c3d74ba97f79e53c73149bee6db5f62a2b9e51'

@magazine_report_blueprint.route('/', methods=['GET'])
def magazine_report_main():
    """
        HTTP Cloud Function.
        Args:
            request (flask.Request): The request object.
            <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
        Returns:
            The response text, or any set of values that can be turned into a
            Response object using `make_response`
            <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    # test mode
    is_test_mode = False
    if request.headers.get('X-Test-Prisma-Api', None) and request.headers['X-Test-Prisma-Api'] in global_x_prisma_auth:
        is_test_mode = True

    logging.info(f'[Magazine_Report] is_test_mode ? {is_test_mode}') 

    # security for non-test mode, aka cron-mode
    if not request.headers.get('X-Appengine-Cron', None) and not is_test_mode:
        return json.dumps({'error': 'permission denied!'}), 403, header_json

    db.create_table()
    now = dt.datetime.now()

    d = request.args.get('d', '')
    if d:
        date = dt.date.fromisoformat(d)
    else:
        date = now.strftime('%Y-%m-%d')
    logging.info(date)

    # fetch reports to send
    reports = db.get_reports(date)

    if reports.result().total_rows == 0:
        msg = 'Hello,\n We found nothing to send for date: {}.\n\n IT-DATA Team'.format(date)
        logging.info(msg)
        if not is_test_mode:
            # send alert email for missing data
            send_email('[Alert] Magazine Report', msg, '<EMAIL>')
        return json.dumps({'status': 'ko', 'error': msg}), 204, header_json

    # Do the stuff and really send reports by email !
    templates = template.get_dartagnan_template()
    for report in reports:
        report_sales_data = db.bq_get_report_sales_data(report['magazine_name'], report['release_number'])
        report_budget_data = db.bq_get_report_budget_data(report['magazine_name'], report['release_number'])
        html = helper.build_email(templates, report, report_sales_data, report_budget_data)

        # add cleost+jtharaud to recipients
        recipients = report['email_recipients'].replace(' ', '')
        recipients = ','.join(list(set(recipients.split(',') + ['<EMAIL>', '<EMAIL>', '<EMAIL>'])))

        subject = report['email_subject']

        if is_test_mode:
            # override everyone for test
            recipients = '<EMAIL>,<EMAIL>'
            subject = '[TEST] ' + report['email_subject']

        try:
            print('. send : ' + subject)
            print('.. to: {}'.format(recipients))
            logging.info(recipients)

            send_email(subject, html, recipients)
        except Exception as e:
            msg = ". Error: unable to send email: {}".format(str(e))
            print(msg)
            return json.dumps({'status': 'ko', 'error': msg}), 500, header_json

        # Add email info to report_email
        if not is_test_mode:
            errors = db.insert_email_details(report['magazine_name'], report['release_number'], now.strftime('%Y-%m-%d'))
            if not errors:
                print(".. Email has been logged as sent to db.")
            else:
                msg = ".. Encountered errors while inserting email log: {}".format(errors)
                print(msg)
                return json.dumps({'status': 'ko', 'more': msg}), 500, header_json

    # return api result
    return json.dumps({'status': 'ok'}), 200, header_json


def send_email(subject, message, destination):
    server = smtplib.SMTP_SSL('smtp.gmail.com', 465)
    smtp_pass = secret.get_secret(os.environ["PROJECT_ID"], os.environ["SECRET_MAGAZINE_REPORT_SMTP_PWD"])
    server.login('<EMAIL>', smtp_pass)

    # Craft message (obj)
    msg = MIMEMultipart()

    msg['Subject'] = subject
    msg['From'] = '<EMAIL>'
    msg['Bcc'] = destination
    msg['reply-to'] = '<EMAIL>'
    # Insert the text to the msg going by e-mail
    msg.attach(MIMEText(message, "html"))
    # send msg
    server.send_message(msg)
