import requests as req
import re
from string import Template


def dartagnan_get_token() -> str:
    """
    Authenticate to dartagnan API and return temporary token

    Returns:
        Auth token
    """
    payload = {
        'client_id': '4a3ad60e8327581355907e8dff0f3cea',
        'client_secret': '588eaa3a79fc089bac6bdb9cede9e1b2',
        'grant_type': 'client_credentials'}

    response = req.request("POST", 'https://app.dartagnan.io/oauth/v2/token', data=payload)

    return response.json()['access_token']


def get_dartagnan_template() -> dict:
    """
    Get Dartagnan template for report email.
    Split the skeleton in 2 parts, and return them as 'skeleton_begin' and 'skeleton_end', with others sections.

    Returns:
        Dartagnan template parts
    """
    url = "https://app.dartagnan.io/api/public/campaigns/1206179/split?encode=0&router=utf8&splitPattern=-_-"

    headers = {
        'Authorization': 'Bearer ' + dartagnan_get_token(),
    }

    response = req.request("GET", url, headers=headers)

    rjson = response.json()

    # split skeleton
    skeleton = re.sub(r'(-_-\s)+', '%SPLIT%', rjson['skeleton'])
    html_begin, html_end = skeleton.split('%SPLIT%')
    # inject custom media styles
    custom_css = '''<style type="text/css">
        @media only screen and (max-width: 400px) {
    .th_width {width: 35px !important ;}
    .tftable, .mobile{font-size: 9px !important;border-spacing:0px !important;}
    .tftable td, .tftable th, .mobile td,.mobile th {padding: 4px !important;}
    }
    @media only screen and (min-width: 767px) {
    .th_width { width: 56px !important;}
    }
    </style>
    '''
    html_begin = html_begin.replace('</head>', custom_css + '</head>')

    sections = {}
    # dynamic sections
    x = re.compile(r"<!-- START OF #(?P<section>\w+) -->(.*?)<!-- END OF #(?P=section) -->", re.MULTILINE | re.DOTALL)
    dynamic_sections = {}
    for key, value in rjson['sections'].items():
        if 'not-used' in key:
            continue
        result = x.findall(value)
        if result:
            for i, t in enumerate(result):
                # add template to dynamic_sections
                dynamic_sections[t[0]] = Template(t[1])
                # remove this fragment/dynamic_section and replace it with ${section_name}
                value = re.sub(r"<!-- START OF #(?P<section>\w+) -->(.*?)<!-- END OF #(?P=section) -->",
                               '${' + t[0] + '}', value, flags=re.MULTILINE | re.DOTALL)
        # add template to sections
        sections[key] = Template(value)

    return {
        'skeleton_begin': Template(html_begin),
        'skeleton_end': Template(html_end),
        **sections,
        **dynamic_sections,
    }

