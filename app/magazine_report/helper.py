from urllib.parse import quote_plus as urlencode
from urllib import request as ulreq
from urllib.error import URLError, HTTPError
from PIL import ImageFile
from typing import Union
import re
import logging

NO_COVER_IMG_URL = 'https://nlimg.prismaconnect-gestion.fr/nl-images/SuiviReportingAbo/no-cover.png'
COVER_IMAGE_WIDTH = 340  # 170 * 2

def build_table(templates: dict, table_data: dict, number):
    """
    Given templates and table data, build the html for the given table.

    Args:
        templates (dict):
        table_data (dict):
        number (int):

    Returns:
        html formatted data

    """
    table = []
    for row in table_data:
        variables = format_row_data(row)
        key = 'table_{}_row'.format(number)

        variables['bg_color'] = '#ffffff'
        variables['style'] = ''
        variables['align'] = 'right'
        if variables['metric_format'] == 'header':
            variables['bg_color'] = '#d2e2f1'
            variables['align'] = 'left'
            variables['style'] = 'font-weight: bold;'
        elif variables['metric_format'] == 'total':
            variables['bg_color'] = '#a1c8e9'
            variables['align'] = 'left'
            variables['style'] = 'font-weight: bold;'

        table.append(templates[key].safe_substitute(**variables))

    return ''.join(table)


def build_email(templates: dict, data: dict, table_data_1: dict, table_data_2: dict):
    """
    Given templates, general data and report data (table 1 & table 2), build the final email.

    Args:
        table_data_1 (dict): data of first table (sales)
        table_data_2 (dict): data of second table (budget)
        templates (dict): html templates for each email parts
        data (dict): email general data

    Returns:
        str: HTML of the email
    """
    begin = ['skeleton_begin', 'header', 'covers', 'blank_line', 'table_1']
    middle = ['blank_line', 'table_2']
    end = ['subtitles', 'comments', 'skeleton_end']

    # convert data from Row()
    data = dict(data)
    data = fix_price(data)
    data = prepare_cover_image(data)
    data['table_1_row'] = build_table(templates, table_data_1, 1)
    data['table_2_row'] = build_table(templates, table_data_2, 2)

    html_parts = [templates[key].safe_substitute(**data) for key in begin]
    html_parts += [templates[key].safe_substitute(**data) for key in middle]
    html_parts += [templates[key].safe_substitute(**data) for key in end]

    return ''.join(html_parts)


def format_row_data(row: dict):
    """
    For report data following several rules

    Args:
        row (dict) : on row of data to format

    Returns:
        html formatted data

    """
    ignore_keys = ['metric_format', 'metric_type', 'metric_order']
    percent_keys = {'unsold_rate', 'previous_unsold_rate', 'previous_year_unsold_rate', 'ytd_vs_previous_ytd_rate',
                    'avg_ytd_vs_previous_ytd_rate', 'sales_volume_vs_target'}
    thousands_kyes = ['sales_volume', 'previous_volume', 'previous_year_volume', 'target_volume', 'sales_volume_ytd',
                      'avg_sales_volume_ytd']
    highlight_keys = percent_keys - {'unsold_rate'}

    do_italic = row['metric_type'] == 'prevision'

    new_row = {}
    for key, value in row.items():
        if key in ignore_keys:
            new_row[key] = value
            continue
        if value is None:
            new_row[key] = '-'
            continue

        # add 'k' unit for sales_volume
        if (key in thousands_kyes) and value > 0:
            if value < 10:
                value = str(value) + ' k'
            else:
                value = str(round(float(value))) + ' k'

        # value is string now
        value = str(value).replace('.', ',')

        if key in percent_keys:
            value = value + '%'

        color = None
        if key in highlight_keys:
            if value[0] == '-':
                color = '#FF0000'  # red
            elif value != '':
                value = '+' + value
                color = '#00A31B'  # green

            if len(value) > 6:
                # limit number to four chars as much as possible.
                # we remove decimals.
                # len("+124,5%") = 7, so "+124,5%" -> "+124%"
                # len("-45,6%") = 6, so modification
                value = re.sub(r',[0-9]%', '%', value)

        if do_italic:
            value = '<i>{}</i>'.format(value)
        if color:
            value = '<span style="color:{color}">{value}</span>'.format(color=color, value=value)

        new_row[key] = value

    return new_row


def fix_price(data: dict) -> dict:
    """
    Fix price information to insert a leading 0

    Args:
        data (dict): report data

    Returns:
        Fixed report data
    """
    keys = ['cur_price', 'prev_price', 'hom_price']
    for k in keys:
        data[k] = data[k].strip(' ')
        if data[k][0] == ',':
            data[k] = '0' + data[k]

    return data


def image_resize_url(url: str,
                     operation: str = 'scale',
                     width: Union[str, int] = 'scale',
                     height: Union[str, int] = 'auto') -> str:
    """
    Use img.pmdstatic.net service to resize image

    Args:
        url (str): original image url
        operation (str): scale|pad|fit
        width (str|int): wanted width or 'auto' for 'scale' operation
        height (str|int): wanted height or 'auto' for 'scale' operation

    Returns:
        Url of the resied image
    """

    if url.startswith('https://img.pmdstatic.net/'):
        # ignore already resize image
        return url

    # encode url for image-resize service
    origin = urlencode(url).replace('.', '%2E').replace('+', '.2520').replace('%', '.')

    return 'https://img.pmdstatic.net/{operation}/{origin}/{width}x{height}/quality/90/i.png'.format(
        operation=operation, origin=origin, width=str(width), height=str(height)
    )


def get_nocover_height(uri: str) -> Union[int, None]:
    """
    Determine the height of an 170 x 2 width-ed image to have the same height x width ratio
    as a given image, got from an remote uri.

    Args:
        uri (str): remove image url

    Returns:
         no cover image height or None
    """
    # get image size and return need height for
    # a 170*2 pixel width image and keep initial (w, h) ratio.
    try:
        file = ulreq.urlopen(uri)
        p = ImageFile.Parser()
        while True:
            data = file.read(1024)
            if not data:
                break
            p.feed(data)
            if p.image:
                w, h = p.image.size
                if w == 0:  # Avoid division by zero
                    return None
                # cross product
                return (h * COVER_IMAGE_WIDTH) // w

        file.close()
    except (HTTPError, URLError) as e:
        logging.error(f"Network error opening URL: {uri}. Error: {e}")
    except Exception as e:
        logging.error(f"Failed to open URL: {uri}. Error: {e}")
    return None


def prepare_cover_image(data: dict) -> dict:
    """
    Compute cur_cover_img, prev_cover_img and hom_cover_img from *_url using image_resize
    and compute the right no-cover image for empty cover.

    Args:
        data (dict): data used to build the report email

    Returns:
        dict: modified data with correct cover images
    """
    keys = ['cur_cover_img_url', 'prev_cover_img_url', 'hom_cover_img_url']

    # determine no-cover height from covers
    # fixme : ugly to use no_cover_img_url, and need to be sync with BQ query
    h = None
    for k in keys:
        if data['cur_cover_img_url'] and data[k] != NO_COVER_IMG_URL:
            h = get_nocover_height(data['cur_cover_img_url'])
            if h:
                break

    if h is None:
        new_no_cover_img_url = NO_COVER_IMG_URL
    else:
        new_no_cover_img_url = image_resize_url(NO_COVER_IMG_URL, width=COVER_IMAGE_WIDTH, height=h)

    # build cur_cover_img from cur_cover_img_url, etc..
    for k in keys:
        if data[k] == NO_COVER_IMG_URL:
            data[k.replace('_url', '')] = new_no_cover_img_url
        else:
            data[k.replace('_url', '')] = image_resize_url(data[k], width=COVER_IMAGE_WIDTH)

    return data
