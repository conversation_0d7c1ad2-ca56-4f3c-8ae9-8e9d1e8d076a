from flask import Blueprint, request
import json
import os
import re
from bs4 import BeautifulSoup
import urllib.request
from urllib.parse import urlparse, unquote, parse_qs
# for gcs
from google.cloud import storage

import app.reelevant.db as db

reelevant_blueprint = Blueprint('reelevant', __name__, url_prefix='/reelevant')

header_json = {'content-type': 'application/json'}

env = os.getenv("ENV")

@reelevant_blueprint.route('/get-links/<router_url>', methods=['GET'])
def reelevant_main(router_url: str):
    """
        HTTP Cloud Function.
        Args:
            router_url: rogue-one router-url used for newsletters
            request (flask.Request): The request object.
            <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
        Returns:
            The response text, or any set of values that can be turned into a
            Response object using `make_response`
            <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    try:
        last_links = []
        nl_url = db.psql_get_nlf_url(router_url)

        if nl_url is None:
            return json.dumps({'status': 'ko', 'error': 'Provided url-router does not exist !'}), 404, header_json

        # get links from today's NL
        with urllib.request.urlopen(nl_url) as connection:
            html = connection.read()
            last_links += extract_links(html.decode("utf-8"), False)
            connection.close()

        reports = db.bq_get_last_campaigns_for(router_url)
        if reports.result().total_rows == 0:
            return json.dumps({'status': 'ko', 'error': 'No links found for provided url-router'}), 404, header_json

        # get links for the last 7 days for this NL
        for report in reports:
            html = read_file(report['message_ref'] + '.html')
            last_links += extract_links(html)

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    # return api result
    return json.dumps(last_links, default=str), 200, header_json


def get_storage_bucket():
    if env != 'prod':
        storage_client = storage.Client('pm-preprod-matrix')
        bucket = storage_client.get_bucket('it-data-preprod-matrix-preprod-pipeline')
    else:
        storage_client = storage.Client('pm-prod-matrix')
        bucket = storage_client.get_bucket('it-data-prod-matrix-pipeline')
    return bucket

def read_file(filename):
    blob = get_storage_bucket().blob('splio/campaign_html/' + filename)
    html = blob.download_as_string()
    return html.decode("utf-8")

def extract_links(html, is_encoded = True):
    domains = [
        'caminteresse.fr',
        'femmeactuelle.fr',
        'serengo.net',
        'gala.fr',
        'capital.fr',
        'voici.fr',
        'businessinsider.fr',
        'neonmag.fr',
        'cuisineactuelle.fr',
        'geo.fr',
        'ohmymag.com',
        'gentside.com',
        'programme-tv.net',
        'programme.tv',
        'hbrfrance.fr',
        'prima.fr',
        'cesoirtv.com',
        'prismaconnect.fr'
    ]
    article_links = []
    soup = BeautifulSoup(html, "html.parser")
    for link in soup.findAll('a'):
        link = link.get('href').lower()
        if not link.startswith('http') or link.startswith('https://file.splio3'):
            # skip links with no http or refers to file
            continue
        if '//dm' in link:
            # skip unsub links
            continue

        # decode url
        url = urlparse(unquote(link)) if is_encoded else urlparse(link)
        # get destination url
        destination_url = urlparse(parse_qs(url.query)['u'][0]) if parse_qs(url.query).get('u', None) else url
        hostnames = destination_url.netloc.split(".")
        # strip-off sub domain or www from host name
        domain = hostnames[len(hostnames)-2] + "." + hostnames[len(hostnames)-1];
        # print(domain)
        if domain not in domains:
            continue
        # print(destination_url)
        article_links.append(destination_url.geturl())
    return set(article_links)