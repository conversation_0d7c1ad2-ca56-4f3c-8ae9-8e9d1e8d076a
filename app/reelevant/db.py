import os
from google.cloud import bigquery

import logging
import sqlalchemy
import secret_manager as secret

logging.basicConfig()  # log messages to stdout
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(logging.INFO)


def get_bq_db():
    """
    # init BQ database
    # @Return bigquery Client
    """
    client = bigquery.Client('pm-prod-matrix')
    env = os.getenv("ENV")
    if env != 'prod':
        client = bigquery.Client('pm-preprod-matrix')
    return client

def get_psql_db():
    """
    # init postgres database
    # @Return sqlalchemy engine
    """
    env = os.environ.get("ENV")

    db_port = os.environ.get("DB_PORT", 5432)
    db_user = os.environ.get("DB_USERNAME_ROGUEONE", 'rogue_one_app')
    db_pass = secret.get_secret(os.environ["PROJECT_ID"], os.environ["SECRET_ROGUEONE_PWD_ID"])
    db_name = os.environ.get("DB_DATABASE", "matrix")
    db_socket_dir = os.environ.get("DB_SOCKET_DIR", "/cloudsql")
    cloud_sql_connection_name = os.environ["DB_CONNEXION_STRING"]

    if env == 'dev':
        #  init tcp connection engine for dev env using cloud sql proxy
        engine_url = sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=db_user,  # e.g. "my-database-user"
            password=db_pass,  # e.g. "my-database-password"
            host=cloud_sql_connection_name,  # e.g. "127.0.0.1"
            port=db_port,  # e.g. 5432
            database=db_name,  # e.g. "my-database-name"
            query = {"connect_timeout": 10}
        )
    else:
        # init unix connection engine
        engine_url = sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=db_user,
            password=db_pass,
            database=db_name,
            host=cloud_sql_connection_name,  # e.g. "127.0.0.1"
            port=db_port,  # e.g. 5432
            query={
                "unix_sock": "{}/{}/.s.PGSQL.5432".format(
                    db_socket_dir,
                    cloud_sql_connection_name)
            }
        )

    db = sqlalchemy.create_engine(
        engine_url,
        pool_size=5,
        max_overflow=2,
        pool_timeout=30,
        pool_recycle=1800,
        # executemany_mode = 'values_plus_batch',
        client_encoding="utf8",
        execution_options={
            "isolation_level": "AUTOCOMMIT"
        }
    )

    return db

def bq_get_last_campaigns_for(url_router: str):
    """
    Get last SPLIO campaigns(message_ref)

    Args:
        url_router (str) : rogue-one public url (consent_ref|theme)

    Returns:
        (list) list of message_ref
    """
    sql = """
        SELECT r.message_ref
        FROM `store_tracking.splio_report` AS r
        JOIN `store_matrix_email.rogue_one_email_consent` AS ec ON SAFE_CAST(r.rogue_one_email_id AS INT64) = ec.rogue_one_email_id
        JOIN `refined_data.email_base` AS eb ON eb.consent_id = ec.email_consent_id
        LEFT JOIN `store_matrix_email.rogue_one_theme` AS t ON ec.rogue_one_email_id = t.rogue_one_email_id
        WHERE DATE(starttime) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
          AND (eb.consent_public_ref = @url_router OR t.theme = @url_router)
        ORDER BY starttime DESC
    """

    job_config = bigquery.QueryJobConfig(
      query_parameters=[
          bigquery.ScalarQueryParameter("url_router", "STRING", url_router)
      ]
    )

    query_result = get_bq_db().query(sql, job_config=job_config)
    return query_result

def psql_get_nlf_url(url_router: str):
    sql = """
          SELECT t.content
          FROM rogue_one.base AS b
          JOIN rogue_one.template AS t ON b.default_template_id = t.id
          WHERE b.router_url = '{url_router}'
    """
    values = { 'url_router': url_router }
    with get_psql_db().connect() as conn:
      query = sql.format(**values)
      template = conn.execute(query).fetchone()
      if template:
          return template["content"].replace("$#$_url=\"", "").replace("\"_$#$", "")
      else:
          return None