# Import flask dependencies
from flask import Blueprint, request
from app.echo_request import echo_request

mirror_blueprint = Blueprint('mirror', __name__, url_prefix='/mirror')


@mirror_blueprint.route('/', methods=['POST', 'GET'])
def mirror_main() -> object:
    """
    HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    return echo_request(request)
