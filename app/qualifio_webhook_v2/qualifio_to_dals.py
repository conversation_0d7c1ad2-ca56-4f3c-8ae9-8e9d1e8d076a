import json
import logging

import flask

import app.qualifio_webhook_v2.campaign_service as cs

db = cs.db_engine

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}


def qualifio_to_dals(request: flask.Request) -> object:
    """

        The purpose of this function is to catch DALS 2022 participation and save it
        into database (esampling.qualifio_to_dals_2022 table).

        See <PERSON><PERSON> https://pmdtech.atlassian.net/browse/ITDATA-2168

        Args:
            request (flask.Request): HTTP request object.

        Returns:
            The response text or any set of values that can be turned into a
            Response object using
            `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.

        CREATE TABLE IF NOT EXISTS esampling.qualifio_to_dals_2022
        (
            qualifio_ref uuid NOT NULL,
            email character varying(120) COLLATE pg_catalog."default",
            firstname character varying(50) COLLATE pg_catalog."default",
            lastname character varying(50) COLLATE pg_catalog."default",
            qualifio_data jsonb NOT NULL,
            create_date timestamp with time zone NOT NULL DEFAULT now(),
            update_date timestamp with time zone,
            CONSTRAINT qualifio_to_dals_2022_pk PRIMARY KEY (qualifio_ref)
        )
        ALTER TABLE esampling.qualifio_to_dals_2022 OWNER TO esampling;
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_dals_2022] request_json : {}'.format(str(request_json)))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    query = '''
        INSERT INTO esampling.qualifio_to_dals_2022
        (qualifio_ref, email, firstname,  lastname, qualifio_data, create_date, update_date)
        VALUES (CAST('{qualifio_ref}' AS uuid), '{email}', '{firstname}', '{lastname}', CAST('{qualifio_data}' AS jsonb), now(), now())
        ON CONFLICT (qualifio_ref) DO UPDATE
            SET qualifio_data = CAST('{qualifio_data}' AS jsonb),
            email = '{email}',
            firstname = '{firstname}', 
            lastname = '{lastname}',
            update_date = NOW()
        ;
    '''

    values = {
        'qualifio_ref': request_json['id'],
        'email': request_json['variables'].get('email'),
        'firstname': request_json['variables'].get('firstname').replace("'", '\\"'),
        'lastname': request_json['variables'].get('lastname').replace("'", '\\"'),
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }

    try:
        with db.connect() as conn:
            query = query.format(**values)
            logging.info('query = {}'.format(query))
            conn.execute(query)
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    return json.dumps({'status': 'ok'}), 200, header_json
