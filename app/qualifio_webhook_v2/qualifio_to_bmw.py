import json
import logging
import os

import flask
import requests
import sqlalchemy

import secret_manager as secret
import app.qualifio_webhook_v2.campaign_service as cs

db_engine = cs.db_engine

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret
header_json = {'content-type': 'application/json'}

DRIFTROCK_API_KEY = secret.get_secret(os.environ["PROJECT_ID"], os.environ["SECRET_BMW_DRIFTROCK_API_KEY"])

driftrock_remote_url = "https://external-apis.driftrock.com/v2/event?api_key={}".format(DRIFTROCK_API_KEY)


def qualifio_to_bmw(request: flask.Request) -> object:
    """
    The purpose of this function is to catch Bmw participation and save it
    into database (esampling.qualifio_to_bmw table).
    Then send request to DriftRock Api

    See Jira https://pmdtech.atlassian.net/browse/ITDATA-3103

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    logging.info('[qualifio_to_bmw] request_json : {}'.format(str(request_json)))

    print('request_json = {}'.format(str(request_json)))

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    values = {
        'qualifio_ref': request_json['id'],
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }
    query = """
            INSERT INTO esampling.qualifio_to_bmw
            (qualifio_ref, qualifio_data, create_date, update_date)
            VALUES (CAST(:qualifio_ref AS uuid), CAST(:qualifio_data AS jsonb), now(), now())
            ON CONFLICT (qualifio_ref) DO UPDATE
                SET 
                qualifio_ref = CAST(:qualifio_ref AS uuid), 
                qualifio_data = CAST(:qualifio_data AS jsonb),
                update_date = NOW()
            ;
        """
    try:
        with db_engine.connect() as conn:
            stmt = sqlalchemy.text(query)
            logging.info('query = {}'.format(stmt))
            conn.execute(stmt, values)
            conn.commit()
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    payload = json.dumps({
        "event_type": "lead",
        "event_name": "Lead received",
        "source_id": "03f38840-3ecb-44f6-88a8-a4032302cad5",
        "fields": {
            "type_achat": "Personnel",
            "title": 'M.' if request_json['variables'].get('gender') == 'Monsieur' else 'Mme',
            "last_name": request_json['variables'].get('lastname'),
            "first_name": request_json['variables'].get('firstname'),
            "email": request_json['variables'].get('email'),
            "phone_number": request_json['variables'].get('phone'),
            "postCode": request_json['variables'].get('zipcode'),
            "marketing_consent": True if len(request_json['optins'][0]['response']) > 0 else False
        }
    })

    print('payload = {}'.format(str(payload)))

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer {}'.format(DRIFTROCK_API_KEY)
    }

    response = requests.request("POST", driftrock_remote_url, headers=headers, data=payload)

    print(response.text)

    return response.text, response.status_code, header_json
