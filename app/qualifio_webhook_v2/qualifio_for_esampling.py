import json
import logging
import sqlalchemy

import app.qualifio_webhook_v2.campaign_service as cs

header_json = {'content-type': 'application/json'}


def qualifio_for_esampling(request):
    """
    webhook qualifio v2 -> API process:

    -- check x-qualifio-auth in HEADER x-qualifio-auth
    -- get campaign.id then check if exist and is active in table esampling.campaign => to get public_ref
    -- save & format date in request.values (.form ?) to <public_ref>__qualifio
    """

    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_for_esampling] request_json : {}'.format(str(request_json)))

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    if 'campaign' not in request_json:
        logging.error('[qualifio_for_esampling] Invalid request')
        return json.dumps({'error': 'Invalid request'}), 400, header_json

    db = cs.db_engine

    binding_key = ['id', 'campaign', 'variables']
    for key in binding_key:
        if key not in request_json:
            logging.error('[qualifio_for_esampling] {key} argument is undefined'.format(key=str(key)))
            return json.dumps({'error': '{key} argument is undefined'.format(key=str(key))}), 400, header_json

    qualifio_campaign_ref = request_json['campaign']['id']
    campaign = cs.check_campaign_for_qualifio_ref(int(qualifio_campaign_ref))
    if not campaign:
        logging.info('[qualifio_for_esampling] Ignore campaign id : {}'.format(str(qualifio_campaign_ref)))
        return json.dumps({'msg': 'campaign id ignored'}), 204, header_json
    logging.info('campaign data : {}'.format(str(campaign)))
    print(campaign)
    fields_mapping = {
        # sql : qualifio
        'firstname': 'firstname',
        'lastname': 'lastname',
        'address1': 'address',
        'postal_code': 'zipcode',
        'city': 'locality',
        'email': 'email',
    }
    qualifio_template = dict()
    for sql_key, qualifio_key in fields_mapping.items():
        qualifio_template[sql_key] = request_json['variables'].get(qualifio_key, '')
    default_values = {
        "qualifio_data": json.dumps(request_json).replace("'", '\\"'),
        "qualifio_ref": str(request_json['id']),
        "public_ref": str(campaign['public_ref']),
        'address2': '',
        'address3': '',
        # 'prospect','inscrit','envoye_logistique','attente_echantillon','attente_avis','avis_poste'
        "status": "inscrit"
    }
    qualifio_template = {**qualifio_template, **default_values}
    query = '''
    INSERT INTO esampling.{public_ref}__qualifio(
        qualifio_ref, firstname, lastname, address1, address2, address3, postal_code, city, email, status,
        qualifio_data, collecte_date)
    VALUES('{qualifio_ref}', '{firstname}', '{lastname}', '{address1}', '{address2}', '{address3}', '{postal_code}',
           '{city}', '{email}', '{status}', '{qualifio_data}', NOW())
    ON CONFLICT(qualifio_ref) DO UPDATE
    SET
        firstname = '{firstname}',
        lastname = '{lastname}',
        address1 = '{address1}',
        address2 = '{address2}',
        address3 = '{address3}',
        postal_code = '{postal_code}',
        city = '{city}',
        email = '{email}',
        status = '{status}',
        qualifio_data = '{qualifio_data}',
        collecte_date = NOW()
    ; '''.format(**qualifio_template)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            conn.execute(stmt)
            return json.dumps({'msg': '[qualifio_for_esampling] contact has been saved successfully'}), 200, header_json
    except Exception as e:
        return json.dumps({'error': '[qualifio_for_esampling] Server Error: {e}'.format(e=str(e))}), 500, header_json
