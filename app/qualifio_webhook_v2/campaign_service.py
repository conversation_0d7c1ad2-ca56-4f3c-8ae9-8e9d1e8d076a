import json
import logging

import sqlalchemy

header_json = {'content-type': 'application/json'}

import app.__gcp_commun_services.cloudsql as cloudsql

db_username = 'esampling_app'
db_engine = cloudsql.init_db_engine(db_username)


def list_campaign():
    """
    # Return list of all active campaign
    # @Return json
    """
    select_query = '''
        SELECT * FROM esampling.campaign
        where is_active = true;
    '''
    stmt = sqlalchemy.text(select_query)
    try:
        with db_engine.connect() as conn:
            campaigns = (conn.execute(stmt)).fetchall()
            if campaigns:
                records = [campaign._asdict() for campaign in campaigns]
                logging.info('records : {}'.format(str(records)))
                return json.dumps(records, default=str), 200, header_json
            # not found
            data = {'error': 'No campaigns were found.'}
            return json.dumps(data, default=str), 204, header_json

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def check_campaign(public_ref, qualifio_campaign_ref=None):
    """
    # Check if a campaign exist (active or not) for a given public_ref
    # @Return json
    """
    query = '''
        SELECT *
        FROM esampling.campaign
        WHERE public_ref = '{public_ref}'
    '''.format(public_ref=public_ref)

    if qualifio_campaign_ref:
        query = '''
            SELECT * FROM esampling.campaign
            WHERE public_ref = '{public_ref}'
            OR qualifio_campaign_ref='{qualifio_campaign_ref}';
        '''.format(public_ref=public_ref, qualifio_campaign_ref=qualifio_campaign_ref)

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            campaign = (conn.execute(stmt)).first()
            if campaign:
                logging.info('campaign : {}'.format(str(campaign._asdict())))
                return campaign._asdict()
            return None
    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return '{"error": "Server Error"}', 500, header_json


def get_campaign_by_qualifio_campaign_ref(qualifio_campaign_ref):
    """
    # get campaign data for given qualifio_campaign_ref
    # Return campaign object
    """
    query = '''
        SELECT *
        FROM esampling.campaign
        where qualifio_campaign_ref = {qualifio_campaign_ref}
        
    '''.format(qualifio_campaign_ref=qualifio_campaign_ref)

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            campaign = (conn.execute(stmt)).first()
            if campaign:
                logging.info('campaign : {}'.format(str(campaign._asdict())))
                return json.dumps(campaign._asdict(), default=str), 200, header_json
            return json.dumps({'msg': 'No campaign was found for the given id : {}'.format(
                qualifio_campaign_ref)}), 204, header_json
    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def check_campaign_for_qualifio_ref(qualifio_campaign_ref):
    """
    # Check if campaign exist & active for given qualifio_campaign_ref
    # Return campaign object
    """
    query = '''
        SELECT *
        FROM esampling.campaign
        where qualifio_campaign_ref = {qualifio_campaign_ref} AND is_active = true
    '''.format(qualifio_campaign_ref=qualifio_campaign_ref)

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            campaign = (conn.execute(stmt)).first()
            if campaign:
                logging.info('campaign : {}'.format(str(campaign._asdict())))
                return campaign._asdict()
            return None
    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def init_campaign_templates(public_ref):
    query = f"""
    CREATE TABLE esampling.{public_ref}__qualifio (
        LIKE esampling.template__qualifio
    );
    ALTER TABLE esampling.{public_ref}__qualifio ADD CONSTRAINT {public_ref}__qualifio_pk PRIMARY KEY (qualifio_ref);
    ALTER TABLE esampling.{public_ref}__qualifio OWNER TO esampling;

    -- holy_sampling table 
    CREATE TABLE esampling.{public_ref}__holy_sampling (
        LIKE esampling.template__holy_sampling
    );
    ALTER TABLE esampling.{public_ref}__holy_sampling OWNER TO esampling;
    ALTER TABLE esampling.{public_ref}__holy_sampling ADD CONSTRAINT {public_ref}__holy_sampling_pk PRIMARY KEY (qualifio_ref);
    ALTER TABLE esampling.{public_ref}__holy_sampling ADD CONSTRAINT {public_ref}__holy_sampling_uq UNIQUE (qualifio_ref);

    ALTER TABLE esampling.{public_ref}__holy_sampling ADD CONSTRAINT {public_ref}__qualifio_fk FOREIGN KEY (qualifio_ref)
    REFERENCES esampling.{public_ref}__qualifio (qualifio_ref) MATCH FULL
    ON DELETE CASCADE ON UPDATE CASCADE;

    -- template notification table 
    CREATE TABLE esampling.{public_ref}__notification (
        LIKE esampling.template__notification
    );
    ALTER TABLE esampling.{public_ref}__notification OWNER TO esampling;

    ALTER TABLE esampling.{public_ref}__notification ADD CONSTRAINT template__qualifio_fk FOREIGN KEY (qualifio_ref)
    REFERENCES esampling.{public_ref}__qualifio (qualifio_ref) MATCH FULL
    ON DELETE CASCADE ON UPDATE CASCADE;

    ALTER TABLE esampling.{public_ref}__notification ADD CONSTRAINT {public_ref}__notification_uq 
    UNIQUE (qualifio_ref, name, template);

    """

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            conn.execute(stmt)
            return json.dumps({'msg': 'new campaign successfully created'}), 200, header_json
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def create_campaign(request_json):
    """
     create new campaign
     @Return json
    """
    request_json['campaign_data'] = json.dumps(request_json['campaign_data'])

    query = '''
        INSERT INTO esampling.campaign(
            public_ref, qualifio_campaign_ref, advertiser, offer, brand,  description, 
            campaign_data, is_active, create_date, update_date)
    	VALUES ('{public_ref}', {qualifio_campaign_ref}, '{advertiser}', '{offer}', '{brand}', '{description}', 
    	CAST('{campaign_data}' AS jsonb), {is_active} , NOW(), NOW());
        '''.format(**request_json)

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            conn.execute(stmt)
            return init_campaign_templates(request_json['public_ref'])
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def update_campaign(campaign, request_json):
    """
    # update exist campaign (all fields except public_ref)
    # @Return json
    """
    request_json['campaign_data'] = json.dumps(request_json['campaign_data'])

    query = '''
    Update esampling.campaign
    SET qualifio_campaign_ref = {qualifio_campaign_ref},
        advertiser='{advertiser}',
        offer='{offer}',
        brand='{brand}',
        campaign_data=CAST('{campaign_data}' AS jsonb),
        description='{description}',
        is_active={is_active},
        update_date=NOW()
    WHERE id = {campaign_id};
    '''.format(**request_json, campaign_id=campaign['id'])

    stmt = sqlalchemy.text(query)
    try:
        with db_engine.connect() as conn:
            conn.execute(stmt)
            return json.dumps({'msg': 'campaign successfully updated'}), 200, header_json

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json
