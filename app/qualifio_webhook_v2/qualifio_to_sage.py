import json
import logging

import flask
import requests as req

import app.qualifio_webhook_v2.campaign_service as cs

db = cs.db_engine

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}
remote_url = 'https://s1265708786.t.eloqua.com/e/f2'

def qualifio_to_sage(request: flask.Request) -> object:
    """
    The purpose of this function is to catch Sage PPE participation and save it
    into database (esampling.qualifio_to_sage table).
    Then send request to Sage Api

    See Jira https://pmdtech.atlassian.net/browse/FLUX-152

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_sage] request_json : {}'.format(str(request_json)))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    query = '''
        INSERT INTO esampling.qualifio_to_sage
        (qualifio_ref, qualifio_data, create_date, update_date)
        VALUES (CAST('{qualifio_ref}' AS uuid), CAST('{qualifio_data}' AS jsonb), now(), now())
        ON CONFLICT (qualifio_ref) DO UPDATE
            SET qualifio_ref = CAST('{qualifio_ref}' AS uuid), qualifio_data = CAST('{qualifio_data}' AS jsonb),
                update_date = NOW()
        ;
    '''

    values = {
        'qualifio_ref': request_json['id'],
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }

    try:
        with db.connect() as conn:
            query = query.format(**values)
            logging.info('query = {}'.format(query))
            conn.execute(query)
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    gdpr = 'No'
    gdpr_text = 'No thanks, not today'
    gdpr_response = request_json['optins'][0]['response']
    if gdpr_response == '1':
        gdpr = 'New'
        gdpr_text = request_json['optins'][0]['description']

    sage_campaign_data = {
        "elqSiteID": "1265708786",
        "elqFormName": "NPS_17Q1_OT_GL_DGWW_NPS_ExternalFormHighScore",
        "emailAddress": request_json['variables'].get('email'),
        "country": "France",
        "utm_campaign": "PRL_21Q4_NCA_FR_prismaSBCPpe",
        "utm_source": "leadAggregator",
        "utm_medium": "leadGenNeo",
        "utm_content": "",
        "utm_term": "",
        "gclid": "",
        "campaignID": "70106000001EV3xAAG",
        "product": "Payroll",
        "content": "CL_ACG_FR_NSS_DEM_BOFU_prismaPESBCP",
        "firstName": request_json['variables'].get('firstname'),
        "lastName": request_json['variables'].get('lastname'),
        "phone": request_json['variables'].get('phone'),
        "contact": "1",
        "optedIn": "1",
        "company": request_json['variables'].get('company'),
        "status": "Responded",
        # "stateOrProvince": "",  # See picklist
        # "employee": "",  # See picklist
        "jobtitle": "",
        # "revenue": "",  # See picklist
        # "industry": "",  # See picklist
        "notes": "",
        "city": "",
        "zip": "",
        "mobilePhone": "",
        "SLXCampaignActionCode": "",
        "SLXResponseDate": "",
        "SLXResponseInterest": "",
        "SLXResponseInterestLevel": "",
        "SLXResponseMethod": "",
        "SLXResponseComment": "",
        "adviser": "",
        "clientID": "",
        "specialityDealer": "",
        "customerGroup": "",
        "leadsourceCH": "",
        "msCampaignID": "07D1C6A5-4BDA-EB11-8FC5-005056A92277",
        # "msCampaignID": request_json['id'],
        "msTelSubject": "PRL_21Q4_NCA_FR_prismaSBCPpe",
        "msTelDescription": "PRL_21Q4_NCA_FR_prismaSBCPpe",
        "msTelPriority": "2",

        "gdpr": gdpr,
        "gdprText": gdpr_text,

        "pageURL": 'https://creas.prismamediadigital.com/_Data_Room/2021/07/Sage/LP1/V4/LP1.html',
        # "pageURL": request_json.get('url')

    }

    print('Data to send to Sage Api = {}'.format(str(sage_campaign_data)))
    result = req.post(remote_url, data=sage_campaign_data)
    print('Sage response code = {}'.format(str(result.status_code)))
    if result.status_code != req.codes.ok:
        print('Sage has rejected our data. status_code = {}'.format(str(result.status_code)))
        print('Response body : {}'.format(result.content))
        return json.dumps({'error': 'remote api failed'}), 542, header_json

    print('Sage has accepted our data')
    print('Response body : {}'.format(result.content))
    return json.dumps({'status': 'Sage has accepted our data'}), 200, header_json


def qualifio_to_sage_pme(request: flask.Request) -> object:
    """
    The purpose of this function is to catch Sage PME participation and save it
    into database (esampling.qualifio_to_sage table).
    Then send request to Sage Api

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_sage] request_json : {}'.format(str(request_json)))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    query = '''
        INSERT INTO esampling.qualifio_to_sage
        (qualifio_ref, qualifio_data, create_date, update_date)
        VALUES (CAST('{qualifio_ref}' AS uuid), CAST('{qualifio_data}' AS jsonb), now(), now())
        ON CONFLICT (qualifio_ref) DO UPDATE
            SET qualifio_ref = CAST('{qualifio_ref}' AS uuid), qualifio_data = CAST('{qualifio_data}' AS jsonb),
                update_date = NOW()
        ;
    '''

    values = {
        'qualifio_ref': request_json['id'],
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }

    try:

        with db.connect() as conn:
            query = query.format(**values)
            logging.info('query = {}'.format(query))
            conn.execute(query)
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    gdpr = 'No'
    gdpr_text = 'No thanks, not today'
    gdpr_response = request_json['optins'][0]['response']
    if gdpr_response == '1':
        gdpr = 'New'
        gdpr_text = request_json['optins'][0]['description']

    sage_campaign_data = {
        "elqSiteID": "1265708786",
        "elqFormName": "NPS_17Q1_OT_GL_DGWW_NPS_ExternalFormHighScore",
        "emailAddress": request_json['variables'].get('email'),
        "country": "France",
        "utm_campaign": "PRL_21Q4_NCA_FR_prismaSBCPpme",
        "utm_source": "leadAggregator",
        "utm_medium": "leadGenNeo",
        "utm_content": "",
        "utm_term": "",
        "gclid": "",
        "campaignID": "70106000001EV42AAG",
        "product": "Payroll",
        "content": "CL_ACG_FR_NSS_DEM_BOFU_prismapmeSBCP",
        "firstName": request_json['variables'].get('firstname'),
        "lastName": request_json['variables'].get('lastname'),
        "phone": request_json['variables'].get('phone'),
        "contact": "1",
        "optedIn": "1",
        "company": request_json['variables'].get('company'),
        "status": "Responded",
        # "stateOrProvince": "",  # See picklist
        # "employee": "",  # See picklist
        "jobtitle": "",
        # "revenue": "",  # See picklist
        # "industry": "",  # See picklist
        "notes": "",
        "city": "",
        "zip": "",
        "mobilePhone": "",
        "SLXCampaignActionCode": "",
        "SLXResponseDate": "",
        "SLXResponseInterest": "",
        "SLXResponseInterestLevel": "",
        "SLXResponseMethod": "",
        "SLXResponseComment": "",
        "adviser": "",
        "clientID": "",
        "specialityDealer": "",
        "customerGroup": "",
        "leadsourceCH": "",
        "msCampaignID": "3B32F065-4CDA-EB11-8FC5-005056A92277",
        # "msCampaignID": request_json['id'],
        "msTelSubject": "PRL_21Q4_NCA_FR_prismaSBCPpme",
        "msTelDescription": "PRL_21Q4_NCA_FR_prismaSBCPpme",
        "msTelPriority": "2",

        "gdpr": gdpr,
        "gdprText": gdpr_text,

        "pageURL": 'https://creas.prismamediadigital.com/_Data_Room/2021/07/Sage/LP1/V4_2/LP1_2.html',
    }

    print('Data PME to send to Sage Api = {}'.format(str(sage_campaign_data)))
    result = req.post(remote_url, data=sage_campaign_data)
    print('Sage API PME response code = {}'.format(str(result.status_code)))
    if result.status_code != req.codes.ok:
        print('Sage has rejected our data. status_code = {}'.format(str(result.status_code)))
        print('Response body : {}'.format(result.content))
        return json.dumps({'error': 'remote api failed'}), 542, header_json

    print('Sage has accepted our data PME')
    print('Response body : {}'.format(result.content))
    return json.dumps({'status': 'Sage has accepted our data'}), 200, header_json
