import json
import logging
import os
import sqlalchemy
import flask

import app.qualifio_webhook_v2.campaign_service as cs
db = cs.db_engine

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}


def qualifio_to_corum(request: flask.Request) -> object:
    """
    The purpose of this function is to catch Corum participation and save it
    into database (esampling.qualifio_to_corum table).

    See <PERSON><PERSON> https://pmdtech.atlassian.net/browse/FLUX-152

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_corum] request_json : {}'.format(str(request_json)))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html

    query = '''
        INSERT INTO esampling.qualifio_to_corum
        (qualifio_ref, civility, email, firstname, lastname, phone, project_value, qualifio_data, create_date, update_date)
        VALUES (CAST('{qualifio_ref}' AS uuid), '{civility}', '{email}', '{firstname}', '{lastname}', '{phone}', {project_value}, CAST('{qualifio_data}' AS jsonb), now(), now())
        ON CONFLICT (qualifio_ref) DO UPDATE
            SET qualifio_ref = CAST('{qualifio_ref}' AS uuid), civility = '{civility}', email = '{email}',
                firstname = '{firstname}', lastname = '{lastname}', phone = '{phone}',
                project_value = {project_value}, qualifio_data = CAST('{qualifio_data}' AS jsonb),
                update_date = NOW(), sent_date = null
        ;
    '''

    # extract project_value from json
    project_value = 0
    for item in request_json['form']:
        if item['id'] == 1852834:
            resp = item['responses'].pop()
            project_value = resp.get('value')
            if project_value is None or len(project_value) == 0:
                project_value = 0

    values = {
        'qualifio_ref': request_json['id'],
        'civility': request_json['variables'].get('gender'),
        'email': request_json['variables'].get('email'),
        'firstname': request_json['variables'].get('firstname'),
        'lastname': request_json['variables'].get('lastname'),
        'phone': request_json['variables'].get('phone'),
        'project_value': float(project_value),
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }
    
    try:
        with db.connect() as conn:
            query = query.format(**values)
            logging.info('query = {}'.format(query))
            conn.execute(query)
            return json.dumps({'msg': 'Success'}), 200, header_json
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json
