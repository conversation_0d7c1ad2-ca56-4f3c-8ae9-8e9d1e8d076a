import json
import logging

import flask
import requests as req

import app.qualifio_webhook_v2.campaign_service as cs

db = cs.db_engine

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}
remote_url = 'https://www.insightondemand.fr/webservice/rest/hyundai/leadgen_media_capital.php'

def qualifio_to_hyundai(request: flask.Request) -> object:
    """
    The purpose of this function is to catch Hyundai participation and save it
    into database (esampling.qualifio_to_hyundai table).
    Then send request to Hyundai Api

    See Jira https://pmdtech.atlassian.net/browse/ITDATA-1144

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_hyundai] request_json : {}'.format(str(request_json)))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    query = '''
        INSERT INTO esampling.qualifio_to_hyundai
        (qualifio_ref, qualifio_data, create_date, update_date)
        VALUES (CAST('{qualifio_ref}' AS uuid), CAST('{qualifio_data}' AS jsonb), now(), now())
        ON CONFLICT (qualifio_ref) DO UPDATE
            SET qualifio_ref = CAST('{qualifio_ref}' AS uuid), qualifio_data = CAST('{qualifio_data}' AS jsonb),
                update_date = NOW()
        ;
    '''

    values = {
        'qualifio_ref': request_json['id'],
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }

    try:
        with db.connect() as conn:
            query = query.format(**values)
            logging.info('query = {}'.format(query))
            conn.execute(query)
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    model = ''
    test_mode = None
    for item in request_json['form']:
        if item['label'] == "Test mode":
            test_mode = 'y' if item['responses'][0]['value'] else None
        elif item['label'] == "Modèle":
            model = item['responses'][0]['label']

    optin_1 = 'oui' if request_json['optins'][0]['response'] else 'non'
    optin_2 = 'oui' if request_json['optins'][1]['response'] else 'non'

    hyundai_campaign_data = {
        "campagne": "HYUNDAI_MEDIA_CAPITAL",
        "origine": "capital",
        "souslevier": "newsletter",
        "campaigncode": "HMF0000391",
        "civ": request_json['variables'].get('gender'),
        "nom": request_json['variables'].get('lastname'),
        "prenom":  request_json['variables'].get('firstname'),
        "telephone": request_json['variables'].get('phone'),
        "email": request_json['variables'].get('email'),
        "cp": request_json['variables'].get('zipcode'),
        "ville": request_json['variables'].get('locality'),
        "modele_essai": model,
        "optin_email": optin_1,
        "optin_call": optin_1,
        "optin_sms": optin_1,
        "optin_postal": optin_1,
        "optin_newsletter": optin_2
    }
    # add testmode if exists
    if test_mode:
        hyundai_campaign_data["testmode"] = "y"

    print('Data to send to Hyundai Api = {}'.format(str(hyundai_campaign_data)))
    result = req.post(remote_url, data=hyundai_campaign_data)
    print('Hyundai response code = {}'.format(str(result.status_code)))
    if result.status_code != req.codes.ok:
        print('Hyundai has rejected our data. status_code = {}'.format(str(result.status_code)))
        print('Response body : {}'.format(result.content))
        return json.dumps({'error': 'remote api failed'}), 542, header_json

    print('Hyundai has accepted our data')
    print('Response body : {}'.format(result.content))
    return json.dumps({'status': 'Hyundai has accepted our data'}), 200, header_json
