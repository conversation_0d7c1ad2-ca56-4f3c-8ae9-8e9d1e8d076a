# Import flask dependencies
import json
import logging

from flask import Blueprint
from flask import request

import app.qualifio_webhook_v2.campaign_service as cs
from app.qualifio_webhook_v2.qualifio_for_esampling import qualifio_for_esampling
from app.qualifio_webhook_v2.qualifio_to_corum import qualifio_to_corum
from app.qualifio_webhook_v2.qualifio_to_dals import qualifio_to_dals
from app.qualifio_webhook_v2.qualifio_to_hyundai import qualifio_to_hyundai
from app.qualifio_webhook_v2.qualifio_to_koh_lanta import qualifio_to_koh_lanta
from app.qualifio_webhook_v2.qualifio_to_sage import qualifio_to_sage, qualifio_to_sage_pme
from app.qualifio_webhook_v2.qualifio_to_bmw import qualifio_to_bmw
from app.qualifio_webhook_v2.qualifio_to_total_energie import  qualifio_to_total_energie

header_json = {'content-type': 'application/json'}
global_x_qualifio_auth = ['ac8f6d5dd716f4dcc59e6f8ec87457b6c8b507b8894a410fa3d26a55ca4c52e4']

global_qualifio_dispatch = {
    # test corum
    893414: qualifio_to_corum,
    # corum prod
    829816: qualifio_to_corum,

    # sage preprod
    934549: qualifio_to_sage,
    # sage prod
    934503: qualifio_to_sage,
    934545: qualifio_to_sage,
    # sage pme
    941823: qualifio_to_sage_pme,
    941824: qualifio_to_sage_pme,
    # koh_lanta
    944909: qualifio_to_koh_lanta,
    947845: qualifio_to_koh_lanta,
    # hyundai
    1017023: qualifio_to_hyundai,

    # DALS TEL LOY 2022
    1093783: qualifio_to_dals,
    1093784: qualifio_to_dals,

    # BMW
    1226023: qualifio_to_bmw,

    # Total Energie
    1579361 : qualifio_to_total_energie,

    # default
    '*': qualifio_for_esampling
}

# Define the blueprint: 'auth', set its url prefix: app.url/auth
webhook_blueprint = Blueprint('webhook', __name__, url_prefix='/qualifio-webhook-v2')


# Set the route and accepted methods
@webhook_blueprint.route('/', methods=['POST'])
def qualifio_webhook():
    if 'x-qualifio-auth' not in request.headers:
        return json.dumps({'error': 'permission denied. no header'}), 403, header_json
    if request.headers['x-qualifio-auth'] not in global_x_qualifio_auth:
        return json.dumps({'error': 'permission denied. bad header value'}), 403, header_json

    request_json = request.get_json(force=True, silent=True)
    print(str(request_json))

    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    print(int(request_json['campaign']['id']))

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html
    real_function = global_qualifio_dispatch.get(
        int(request_json['campaign']['id']),
        global_qualifio_dispatch['*']
    )
    logging.info('go to function : {}'.format(real_function))
    print('execute function: {}'.format(real_function))
    print(str(request_json))

    return real_function(request)


campaign_blueprint = Blueprint('campaign', __name__, url_prefix='/esampling-campaign')


@campaign_blueprint.route('/', methods=['GET', 'POST', 'PUT'])
def esampling_campaign_management():
    # authentication via header
    if 'x-qualifio-auth' not in request.headers:
        return '{"error": "permission denied. no header"}', 403, header_json

    if request.headers['x-qualifio-auth'] not in global_x_qualifio_auth:
        return '{"error": "permission denied. bad header value"}', 403, header_json

    # GET = list campaigns , POST = create campaign, PUT = update campaign
    if request.method == 'GET':
        return cs.list_campaign()

    elif request.method == 'POST' or request.method == 'PUT':
        request_json = request.get_json()

        if request_json is None:
            return json.dumps({'error': 'Invalid request'}), 400, header_json

        binding_key = ['public_ref', 'qualifio_campaign_ref', 'campaign_data', 'offer', 'brand', 'advertiser']
        for key in binding_key:
            if key not in request_json:
                return json.dumps({'error': '{key} argument is undefined'.format(key=str(key))}), 400, header_json

        if 'is_active' not in request_json:
            request_json['is_active'] = True

        default_keys = ['description']

        for key in default_keys:
            if key not in request_json:
                request_json[key] = ''

        public_ref = request_json['public_ref']
        qualifio_campaign_ref = request_json['qualifio_campaign_ref']
        campaign = cs.check_campaign(public_ref=public_ref, qualifio_campaign_ref=qualifio_campaign_ref)

        if request.method == 'PUT':

            if not campaign:
                print('[qualifio_template] Ignore campaign for public_ref : {}'.format(str(public_ref)))
                return '{"msg": "campaign id ignored"}', 204, header_json
            return cs.update_campaign(campaign, request_json)
        else:
            if campaign:
                return json.dumps(
                    {'error': 'public_ref or qualifio_campaign_ref already used in campaign'}), 400, header_json
            return cs.create_campaign(request_json)
    else:
        return json.dumps({'error': 'Not supported request method'}), 400, header_json


campaign_data_blueprint = Blueprint('campaign_data', __name__,
                                    url_prefix='/esampling-campaign/<int:qualifio_campaign_ref>')


@campaign_data_blueprint.route('/', methods=['GET'])
def esampling_get_campaign(qualifio_campaign_ref):
    # authentication via header
    if 'x-qualifio-auth' not in request.headers:
        return '{"error": "permission denied. no header"}', 403, header_json

    if request.headers['x-qualifio-auth'] not in global_x_qualifio_auth:
        return '{"error": "permission denied. bad header value"}', 403, header_json

    return cs.get_campaign_by_qualifio_campaign_ref(qualifio_campaign_ref)
