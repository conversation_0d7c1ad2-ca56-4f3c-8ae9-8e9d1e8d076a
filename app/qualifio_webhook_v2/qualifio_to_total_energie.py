import json
import logging
import os
import urllib.parse

import flask
import requests as req
import sqlalchemy

import app.__gcp_commun_services.cloudsql as cloudsql

db_username = 'esampling_app'
db_engine = cloudsql.init_db_engine(db_username)

# disable FTM, while migrating to AppEngine
# from secret_manager import get_secret

header_json = {'content-type': 'application/json'}


def qualifio_to_total_energie(request: flask.Request) -> object:
    """
    The purpose of this function is to catch total_energie PPE participation and save it
    into database (esampling.qualifio_to_total_energie table).
    Then send request to total_energie Api

    See Jira https://pmdtech.atlassian.net/browse/ITDATA-5853

    Args:
        request (flask.Request): HTTP request object.

    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <https://flask.palletsprojects.com/en/1.1.x/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json(force=True, silent=True)
    logging.info('[qualifio_to_total_energie] request_json : {}'.format(str(request_json)))
    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    # request_json format : https://integrations.qualifio.com/schemas/webhook/v2/index.html

    qualifio_ref = request_json['id']

    values = {
        'qualifio_ref': qualifio_ref,
        'qualifio_data': json.dumps(request_json).replace("'", '\\"')
    }
    query = """
            INSERT INTO esampling.qualifio_to_total_energie
            (qualifio_ref, qualifio_data, create_date, update_date)
            VALUES (CAST(:qualifio_ref AS uuid), CAST(:qualifio_data AS jsonb), now(), now())
            ON CONFLICT (qualifio_ref) DO UPDATE
                SET 
                qualifio_ref = CAST(:qualifio_ref AS uuid), 
                qualifio_data = CAST(:qualifio_data AS jsonb),
                update_date = NOW()
            ;
        """
    try:
        with db_engine.connect() as conn:
            stmt = sqlalchemy.text(query)
            logging.info('query = {}'.format(stmt))
            conn.execute(stmt, values)
            conn.commit()
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json

    ## send data to Total Energie
    qualifio_variables = request_json['variables']
    logging.info('Data from qualifio = {}'.format(str(qualifio_variables)))

    id_mapping = {
        3187021: 'tva',
        3187004: 'company',
        3186989: 'siren',
        3187003: 'nace',
        3187022: 'function',
        3035601: 'mobile',
        3186997: 'phone',
    }

    # Iterate through the form items and extract the required fields
    for item in request_json['form']:
        if item['id'] in id_mapping and id_mapping[item['id']] not in qualifio_variables:
            field_name = id_mapping[item['id']]
            value = item['responses'][0]['value'] if item['responses'] else ''
            qualifio_variables[field_name] = value

    logging.info('Completed data from qualifio = {}'.format(str(qualifio_variables)))
    # check required params
    required_params = ['email', 'firstname', 'lastname', 'address', 'zipcode', 'locality',
                       'country', 'tva', 'company', 'siren', 'nace', 'function', 'phone', 'mobile']

    for param in required_params:
        if param not in qualifio_variables:
            return json.dumps({'error': f'Missing required parameter: {param}'}), 400, header_json

    mobile = qualifio_variables.get('mobile')
    phone = qualifio_variables.get('phone')

    if mobile and mobile.startswith(("01", "02", "03", "04", "05", "09")):
        phone = mobile

    if phone and phone.startswith(("06", "07")):
        mobile = phone

    country = qualifio_variables.get('country')
    country_iso_code = country[:2].upper()
    te_payload = {
        # lead personal infos
        "f_1_email": qualifio_variables.get('email'),
        "f_3_firstname": qualifio_variables.get('firstname'),
        "f_4_lastname": qualifio_variables.get('lastname'),

        "f_6_address1": qualifio_variables.get('address'),
        "f_39_zipcode": qualifio_variables.get('zipcode'),
        "f_40_city": qualifio_variables.get('locality'),
        "f_201_country": country_iso_code,

        "f_12_phone1": phone,
        "f_15_mobile": mobile,

        # entreprise
        "f_199_company_name": qualifio_variables.get('company'),
        "f_266_registrationnumber": qualifio_variables.get('siren'),  # SIREN
        "f_960_sic": qualifio_variables.get('nace'),  # Code NACE
        "f_959_legalform": qualifio_variables.get('function'),  # Forme juridique
        "f_958_fiscalid": qualifio_variables.get('tva'),  # Num TVA

        "sid": "463",
        "cid": "100",
        "f_271_campagneproduit": "CFLEET",
        "f_272_canauxmarketing": "EMAIL",
        "f_273_volume": "1",
        "f_992_additionalinformation": "",

        # fixed values
        "f_962_externalmanager": "true",
        "f_925_origincode": "34",
        "f_961_phonecountrycode": "+33"  # fixed
    }

    # URL encode the payload with UTF-8
    te_encoded_payload = urllib.parse.urlencode(te_payload, encoding='utf-8')

    update_payload_query = """
        UPDATE esampling.qualifio_to_total_energie
        SET lead_data = :te_payload
        WHERE qualifio_ref = :qualifio_ref
    """
    update_payload_values = {
        'te_payload': json.dumps(te_payload),
        'qualifio_ref': qualifio_ref
    }

    try:
        with db_engine.connect() as conn:
            update_payload_stmt = sqlalchemy.text(update_payload_query)
            conn.execute(update_payload_stmt, update_payload_values)
            conn.commit()
    except Exception as e:
        logging.error(f"Error updating database with Total Energie payload: {e}")

    # Calculate Content-Length based on the encoded payload
    content_length = len(te_encoded_payload)

    te_headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
        "Content-Length": str(content_length),
    }

    logging.info('Data to send to total_energie Api = {}'.format(str(te_payload)))

    if os.environ.get("ENV") != 'prod':
        return json.dumps({'exit': 'Not a production env!'}), 200, header_json

    te_url = 'https://neo.databowl.com/api/v1/lead'
    try:
        result = req.post(te_url, data=te_encoded_payload, headers=te_headers)
        result.raise_for_status()
        response_text = result.text
        response_code = result.status_code
    except req.exceptions.RequestException as e:
        logging.error('Failed to send data to total_energie Api. Status code: %d, Response: %s',
                      e.response.status_code if e.response else 500,
                      e.response.text if e.response else str(e))
        response_text = e.response.text if e.response else str(e)
        response_code = e.response.status_code if e.response else 500

    logging.info('total_energie response code = {}'.format(str(response_code)))
    logging.info('total_energie response body = {}'.format(str(response_text)))

    update_query = """
         UPDATE esampling.qualifio_to_total_energie
         SET response_code = :te_response_code, response_body = :te_response_body, update_date = NOW()
         WHERE qualifio_ref = :qualifio_ref
     """
    update_values = {
        'te_response_code': response_code,
        'te_response_body': response_text,
        'qualifio_ref': qualifio_ref
    }
    try:
        with db_engine.connect() as conn:
            update_stmt = sqlalchemy.text(update_query)
            conn.execute(update_stmt, update_values)
            conn.commit()
    except Exception as e:
        logging.error(f"Error updating database with Total Energie response: {e}")
        return json.dumps({
            'success': False,
            'error': 'remote api failed',
            'error_detail': f"Error updating database with Total Energie response: {e}"
        }), 542, header_json

    if response_code != req.codes.ok:
        return json.dumps({
            'success': False,
            'error': 'remote api failed',
            'error_detail': response_text
        }), 542, header_json
    logging.info('Successfully sent data to total_energie Api')
    return json.dumps({
        'success': True,
        'status': 'total_energie has accepted our data',
        'response': response_text
    }), 200, header_json
