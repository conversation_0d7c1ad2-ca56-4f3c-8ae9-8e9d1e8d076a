import os

# for BigQuery
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import logging

# BigQuery client
client = bigquery.Client('pm-prod-matrix')

env = os.getenv("ENV")
if env != 'prod':
    client = bigquery.Client('pm-preprod-matrix')

def create_table():
    dataset_ref = client.dataset('store_beop')
    # Prepares a reference to the table
    table_ref = dataset_ref.table('import_payload')
    try:
        table = client.get_table(table_ref)
        logging.info('trying to get table store_beop.import_payload')
    except NotFound:
        # if we don't find the table, create it
        schema = [
            bigquery.SchemaField('json_payload', 'STRING', mode='REQUIRED',
                                 description='raw payload from beop')
        ]
        table = bigquery.Table(table_ref, schema=schema)
        table = client.create_table(table)
        print('table {} created.'.format(table.table_id))
        logging.info('table {} created.'.format(table.table_id))

    return table


def insert_email_details(json_payload):
    table = client.get_table('store_beop.import_payload')  # API call

    logging.info("json_payload: {}".format(json_payload))

    rows_to_insert = [
        {u"json_payload": json_payload},
    ]
    errors = client.insert_rows(table, rows_to_insert)  # API request

    return errors


def get_nb_lines():
    """
    Get the number of lines in the table

    Returns:
        The number of lines inside the table store.beop_payload
    """
    sql = """
        SELECT
            count(*) size
        FROM `store_beop.import_payload`
    """

    query_result = client.query(sql)

    for row in query_result:
        table_size = row['size']

    return table_size
