from flask import Blueprint, request
import json
import sys
import logging
import os

import app.beop.db as db

beop_blueprint = Blueprint('beop', __name__, url_prefix='/beop')

header_json = {'content-type': 'application/json'}


@beop_blueprint.route('/', methods=['POST'])
def beop_store_main():
    """
        Used to store json_payload form beop into BQ store.beop_payload
        Args:
            
        Returns:
            
    """

    request_json = request.get_json(force=True, silent=True)

    # create table store.beop_payload
    table = db.create_table()
    if not table:
        return json.dumps({'status': 'ko'}), 224, header_json

    # get number of line before
    before = db.get_nb_lines()

    # insert new line into the table store.beop_payload
    db.insert_email_details(str(request_json))

    # get number of line after
    after = db.get_nb_lines()

    msg1 = f"We had {before} lines in our table."
    msg2 = f"We now have {after} lines in our table."

    logging.info(msg1)
    logging.info(msg2)

    # return api result
    return json.dumps({'status': 'ok'}), 200, header_json
