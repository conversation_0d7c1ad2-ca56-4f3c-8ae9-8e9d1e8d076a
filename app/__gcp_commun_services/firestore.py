from google.cloud import firestore
import os

class FirestoreManager:
    def __init__(self, project_id=None):
        self.project_id = project_id or os.getenv('PROJECT_ID')
        self.db = firestore.Client(project=self.project_id)

    def get_collection(self, collection_id):
        """Returns a CollectionReference for the given collection_id."""
        return self.db.collection(collection_id)

    def get_document(self, collection_id, document_id):
        """Returns a DocumentReference for the given collection_id and document_id."""
        return self.get_collection(collection_id).document(document_id)

    def add_document(self, collection_id, document_data):
        """Adds a new document with document_data to the collection_id.
           Returns the DocumentReference of the newly created document."""
        return self.get_collection(collection_id).add(document_data)[1]

    def update_document(self, collection_id, document_id, document_data):
        """Updates the document specified by collection_id and document_id with document_data.
           Returns the DocumentReference of the updated document."""
        doc_ref = self.get_document(collection_id, document_id)
        doc_ref.update(document_data)
        return doc_ref

    def delete_document(self, collection_id, document_id):
        """Deletes the document specified by collection_id and document_id.
           Returns the DocumentReference of the deleted document."""
        doc_ref = self.get_document(collection_id, document_id)
        doc_ref.delete()
        return doc_ref

    def get_all_documents(self, collection_id):
        """Returns a stream of all documents in the specified collection_id."""
        return self.get_collection(collection_id).stream()

    def get_document_by_field(self, collection_id, field, value):
        """Returns a stream of documents in collection_id where the given field matches the value."""
        return self.get_collection(collection_id).where(field, '==', value).stream()

    def get_document_by_query(self, collection_id, query):
        """Returns a stream of documents in collection_id matching the provided query conditions."""
        return self.get_collection(collection_id).where(*query).stream()

