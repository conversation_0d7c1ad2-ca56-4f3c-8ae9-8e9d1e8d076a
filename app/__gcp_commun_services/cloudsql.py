import logging
import os

import sqlalchemy

import secret_manager as secret

logging.basicConfig()  # log messages to stdout
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(logging.INFO)

env = os.environ.get("ENV")
safe_project = os.environ.get("SAFE_PROJECT_ID", 'pm-preprod-safe')


def init_db_engine(db_username):
    """
    # init database
    # @Return sqlalchemy engine
    """
    db_port = os.environ.get("DB_PORT", 5432)
    db_user = db_username
    db_pass_secret_id = f'itdata_psql_password-{db_username}'
    db_pass = secret.get_secret(safe_project, db_pass_secret_id)
    db_name = os.environ.get("DB_DATABASE", "matrix")
    db_socket_dir = os.environ.get("DB_SOCKET_DIR", "/cloudsql")
    # i.e "<PROJECT-NAME>:<INSTANCE-REGION>:<INSTANCE-NAME>"
    cloud_sql_connection_name = os.environ["DB_CONNEXION_STRING"]

    if env == 'dev':
        return sqlalchemy.create_engine(
            f"postgresql+pg8000://{db_username}:{db_pass}@{cloud_sql_connection_name}:{db_port}/{db_name}",
            connect_args={"timeout": 30},
        )
    else:
        # init unix connection engine
        engine_url = sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=db_user,
            password=db_pass,
            database=db_name,
            host=cloud_sql_connection_name,  # e.g. "127.0.0.1"
            port=db_port,  # e.g. 5432
            query={
                "unix_sock": "{}/{}/.s.PGSQL.5432".format(
                    db_socket_dir,
                    cloud_sql_connection_name)
            }
        )

    try:
        db = sqlalchemy.create_engine(
            engine_url,
            pool_size=5,
            max_overflow=2,
            pool_timeout=30,
            pool_recycle=1800,
            # executemany_mode = 'values_plus_batch',
            client_encoding="utf8",
            execution_options={
                "isolation_level": "AUTOCOMMIT"
            }
        )

        return db
    except TypeError as e:
        print(e)  # Output: Query dictionary values must be strings or sequences of strings
        return None

def table_exists(engine, table_name, schema=None):
    """
    Checks if a table exists in a PostgreSQL schema using SQLAlchemy.

    Args:
        engine: SQLAlchemy engine object.
        table_name: Name of the table to check.
        schema: Name of the schema (optional). If None, the default schema is used.

    Returns:
        True if the table exists, False otherwise.
    """
    inspector = sqlalchemy.inspect(engine)
    return inspector.has_table(table_name, schema=schema)