# Import flask dependencies
import json
import logging
import re

from email_validator import validate_email
from flask import Blueprint, request
from pyisemail import is_email

email_validity = Blueprint('email_validity', __name__, url_prefix='/email-validity')

header_json = {
    'Content-Type': 'application/json',
    "Access-Control-Allow-Origin": "*"
}

# invalid hostname patterns
regex_invalid_hostname_patterns = [
    r'^agora\.bungi\.com$',
    r'^amen\.fr$',
    r'^bungi\.com$',
    r'^emailvision\.com$',
    r'^france-strategies\.com$',
    r'^gandi\.net$',
    r'^illiad\.fr$',
    r'^justice\.fr$',
    r'^koszmail\.pl$',
    r'^node\.com$',
    r'^operamail\.com$',
    r'^ovh\.net$',
    r'^proxad\.net$',
    r'^sdgsd\.com$',
    r'^tim\.it$',
    r'^tiscali\.fr$',
    r'^totalcommunications\.com$',
    r'^wrwer\.com$',
    r'^anacode\.com$',
    r'^ambassade-\+$',
    r'^caramail\..+$',
    r'^fre\..+$',
    r'^freee\..+$',
    r'^gmai\..+$',
    r'^gmaill\..+$',
    r'^hotmai\..+$',
    r'^hotmaill\..+$',
    r'^lycos.+$',
    r'^spamgourmet\.+$',
    r'^tele2\.+$',
    r'^test\.+$',
    r'^voila\..+$',
    r'^yaho\..+$',
    r'^yahooo\..+$',
    r'^yopmail.+$',
    r'^.+\.bg$',
    r'^.+\.cn$',
    r'^.+\.coom$',
    r'^.+\.f$',
    r'^.+\.ffr$',
    r'^.+\.frr$',
    r'^.+\.r$',
    r'^.+\.ru$',
    r'^.+\.th$',
    r'^.+otherinbox.com$',
    r'^.+\.gouv\.fr$',
    r'avocat',
    r'dgccrf',
    r'signal-spam',
    r'ratedane\.com$'
]

# invalid username patterns
regex_invalid_username_patterns = [
    r'^charonne.cassidy$',
    r'^(|.*[-_.])abuse(|[-_.].*)$',
    r'^(|.*[-_.])admins?(|[-_.].*)$',
    r'^(|.*[-_.])contacts?(|[-_.].*)$',
    r'^(|.*[-_.])host(s|master?)?(|[-_.].*)$',
    r'^(|.*[-_.])infos?(|[-_.].*)$',
    r'^(|.*[-_.])ipadmin(|[-_.].*)$',
    r'^(|.*[-_.])noc(|[-_.].*)$',
    r'^(|.*[-_.])postmaster(|[-_.].*)$',
    r'^(|.*[-_.])pourriel(|[-_.].*)$',
    r'^(|.*[-_.])roots?(|[-_.].*)$',
    r'^(|.*[-_.])security(|[-_.].*)$',
    r'^(|.*[-_.])spam(|[-_.].*)$',
    r'^(|.*[-_.])tech(|[-_.].*)$',
    r'^(|.*[-_.])webmasters?(|[-_.].*)$',
    r'^(|.*[-_.])maddox(|[-_.].*)$',
    r'^(|.*[-_.])nexus(|[-_.].*)$',
    r'^(|.*[-_.])test(|[-_.].*)$'
]


@email_validity.route('/', methods=['POST'])
def email_validity_main():
    request_json = request.get_json(force=True, silent=True)
    if request_json is None:
        return json.dumps({'error': 'Invalid request'}), 400, header_json
    if 'email' not in request_json:
        return json.dumps({'error': 'Email argument undefined'}), 400, header_json

    email = request_json.get('email')

    # Convert email to lowercase
    email = email.lower()
    is_valid = False
    # Splio Email Validation Rules  -    Comments
    # [General Structure] email address is structured as <local part>@<domain part>
    # Comment: <EMAIL> is allowed (tld == Top Level Domain)
    email_part = email.split('@')

    if len(email_part) != 2:
        message = f'Email not valid ! email address must be structured as <local part>@<domain part> (email: {email})'
        return __build_response(email, is_valid, message)
    local_part = email_part[0]
    domain_part = email_part[1]

    # [Size of <local part>] 1 <= size(local part) <= 64 characters
    # Comment:
    if len(local_part) > 64 or len(local_part) < 1:
        message = f'Email not valid ! 1 <= size(local part) <= 64 characters (email: {email})'
        return __build_response(email, is_valid, message)

    # [Allowed characters in All parts] lowercase ASCII characters: a to z
    # [Additional Allowed characters in <local part>] digits 0 to 9
    # [Additional Allowed characters in <local part>] special characters: - _ . +
    regex = r'^[a-z0-9\-_.+]+$'

    if not re.match(regex, local_part):
        message = f'Email not valid ! Allowed chars in local_part : a-z, 0-9, -, _, ., + (email: {email})'
        return __build_response(email, is_valid, message)

    # [Restriction in <local part>] The '.' (dot) character CANNOT be the first nor the last character
    if local_part[0] == '.' or local_part[-1] == '.':
        message = f"Email not valid ! The '.' (dot) character CANNOT be the first nor the last character (email: {email})"
        return __build_response(email, is_valid, message)

    # [Restriction in <local part>] The '.' (dot) character CANNOT appear consecutively
    if '..' in local_part:
        message = f"Email not valid ! The '.' (dot) character CANNOT appear consecutively (email: {email})"
        return __build_response(email, is_valid, message)

    # [Restriction in <local part>] The '.' (dot) character CANNOT be the only character
    if local_part == '.':
        message = f"Email not valid ! The '.' (dot) character CANNOT be the only character (email: {email})"
        return __build_response(email, is_valid, message)

    # check invalid username/local part
    if any(re.match(regex_local_part, local_part) for regex_local_part in regex_invalid_username_patterns):
        message = f'Email not valid ! The username = "{local_part}" is not accepted (email: {email})'
        return __build_response(email, is_valid, message)

    # [Domain Part Structure] domain part is structured as < domain >.< tld >
    if '.' not in domain_part:
        message = f'Email not valid ! domain part is structured as < domain >.< tld > (email: {email})'
        return __build_response(email, is_valid, message)

    # [Additional Allowed characters in < domain >] digits 0 to 9
    # [Additional Allowed characters in < domain >] printable characters:.-_
    domain_part = domain_part.lower()
    regex = r'^[a-z0-9][a-z0-9-]*(\.[a-z0-9][a-z0-9-]*)*$'
    if not re.match(regex, domain_part):
        message = f'Email not valid ! Domain must contain only letters, numbers, - and ., starting with a letter or number (email: {email})'
        return __build_response(email, is_valid, message)

    # [Restriction in <domain>] The '.' (dot) character CANNOT appear consecutively
    if '..' in domain_part:
        message = f"Email not valid ! The '.' (dot) character CANNOT appear consecutively (email: {email})"
        return __build_response(email, is_valid, message)

    # [Restriction in <tld>] Last part of domain cannot contain - or _
    domain_part_part = domain_part.split('.')
    tld_part = domain_part_part[-1]
    if '-' in tld_part or '_' in tld_part:
        message = f'Email not valid ! Last part of domain cannot contain - or _ (email: {email})'
        return __build_response(email, is_valid, message)

    # check invalid hostname domains
    if any(re.match(regex_hostname, domain_part) for regex_hostname in regex_invalid_hostname_patterns):
        message = f'Email not valid ! The hostname = "{domain_part}" is not accepted (email: {email})'
        return __build_response(email, is_valid, message)

    # check with email validator
    try:
        # Check that the email address is valid.
        validation = validate_email(email, check_deliverability=True)
        # Take the normalized form of the email address
        # for all logic beyond this point (especially
        # before going to a database query where equality
        # may not take into account Unicode normalization).
        # email = validation.email
    except Exception as e:
        return __build_response(email, is_valid, f'{str(e)} (email: {email})')

    check_dns = is_email(email, check_dns=True)
    if not check_dns:
        dns_error = is_email(email, check_dns=True, diagnose=True)
        message = 'Email not valid ! The hostname = "{domain_part}" has invalid DNS. {dns_error}'.format(
            domain_part=domain_part, dns_error=str(dns_error))
        return __build_response(email, is_valid, message)

    message = 'Email is valid'
    is_valid = True
    return __build_response(email, is_valid, message)


def __build_response(email, is_valid, message):
    body = {
        "email": email,
        "message": message
    }
    if not is_valid:
        body = {
            "email": email,
            "error": message
        }
        logging.error(message)
        return json.dumps(body), 406, header_json

    logging.info(message)
    return json.dumps(body), 200, header_json
