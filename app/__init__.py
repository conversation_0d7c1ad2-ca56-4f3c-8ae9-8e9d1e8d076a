# Import flask and template operators
import os

from flask import Flask, render_template
from flask_cors import CORS

from app.beop.controllers import beop_blueprint
from app.data_enrich.controllers import enrich_get_blueprint, enrich_push_blueprint, \
    container_manager_blueprint
from app.email_validity.controllers import email_validity
from app.gcp_monitor.warmup import warmup_blueprint
from app.get_token.controllers import auth_blueprint
from app.magazine_report.controllers import magazine_report_blueprint
from app.mirror.controllers import mirror_blueprint
from app.notification.controllers import notification_blueprint
from app.notify_me.controllers import notify_me_blueprint
from app.qualifio_to_audika.controllers import qualifio_to_audika_blueprint
from app.qualifio_webhook_v2.controllers import webhook_blueprint, campaign_blueprint, campaign_data_blueprint
from app.redirect_token_distributor.controllers import distrib_blueprint
from app.reelevant.controllers import reelevant_blueprint
from app.rogue_one_tracking.controllers import rogue_one_track_blueprint

# Define the WSGI application object
app = Flask(__name__)
CORS(app)

app.url_map.strict_slashes = False

# app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Configurations
app.config.from_object('config')


# Define the database object which is imported
# by modules and controllers
# db = SQLAlchemy(app)


# Sample HTTP error handling
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html', error=error), 404


env = os.getenv('ENV', '')
if env and env != 'dev':
    import google.cloud.logging

    # Instantiates a client
    client = google.cloud.logging.Client()

    # Retrieves a Cloud Logging handler based on the environment
    # you're running in and integrates the handler with the
    # Python logging module. By default, this captures all logs
    # at INFO level and higher
    client.get_default_handler()
    client.setup_logging()

# Register blueprint(s)

# warmup
app.register_blueprint(warmup_blueprint)

# mirror
app.register_blueprint(mirror_blueprint)
# qualifio webhook blueprints
app.register_blueprint(webhook_blueprint)
app.register_blueprint(campaign_data_blueprint)
app.register_blueprint(campaign_blueprint)

# Qualifio to audika
app.register_blueprint(qualifio_to_audika_blueprint)

# token firebase
app.register_blueprint(auth_blueprint)

# data enrich blueprints
app.register_blueprint(container_manager_blueprint)

app.register_blueprint(enrich_get_blueprint)
app.register_blueprint(enrich_push_blueprint)

# rogue one tracking
app.register_blueprint(rogue_one_track_blueprint)

app.register_blueprint(magazine_report_blueprint)

# notify-me
app.register_blueprint(notify_me_blueprint)

# notification
app.register_blueprint(notification_blueprint)

# BeOP
app.register_blueprint(beop_blueprint)

# Reelevant
app.register_blueprint(reelevant_blueprint)

# email-validity
app.register_blueprint(email_validity)

# redirect token distributor
app.register_blueprint(distrib_blueprint)

#
# Build the database:
# This will create the database file using SQLAlchemy
# db.create_all()
