import json
import logging

import sqlalchemy

from app.data_enrich.db import init_db

header_json = {'content-type': 'application/json'}
# init database connection engine
db = init_db()


def list_container():
    """
    # Return list of all containers
    # @Return json
    """
    select_query = '''
        SELECT * FROM data_stockpile.container
    '''
    stmt = sqlalchemy.text(select_query)
    try:
        with db.connect() as conn:
            containers = (conn.execute(stmt)).fetchall()
            if containers:
                records = [container._asdict() for container in containers]
                logging.info('containers : {}'.format(str(records)))
                return json.dumps(records, default=str), 200, header_json
            # not found
            data = {'error': 'No containers were found.'}
            return json.dumps(data, default=str), 204, header_json

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def create_container(request_json):
    """
     create new container
     @Return json
    """

    query = '''
        -- create container
        INSERT INTO data_stockpile.container(name, active, create_date)
            VALUES ('{name}', true, NOW());
            
        '''.format(**request_json)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            with conn.begin():
                conn.execute(stmt)
                return init_container_templates(request_json['name'])
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def init_container_templates(name):

    query = """
        -- rawdata container
        CREATE TABLE data_stockpile.{name}_rawdata (
            key varchar(64) NOT NULL,
            data jsonb NOT NULL,
            create_date timestamp with time zone NOT NULL DEFAULT now(),
            update_date timestamp with time zone,
            CONSTRAINT {name}_rawdata_key_pk PRIMARY KEY (key)
        );
        ALTER TABLE data_stockpile.{name}_rawdata OWNER TO data_stockpile;
        GRANT SELECT ON TABLE data_stockpile.{name}_rawdata TO all_read_only;
        
        -- data container
        CREATE TABLE data_stockpile.{name}_data (
            key varchar(64) NOT NULL,
            data jsonb NOT NULL,
            create_date timestamp with time zone NOT NULL DEFAULT now(),
            update_date timestamp with time zone,
            CONSTRAINT {name}_data_key_pk PRIMARY KEY (key)
        );
        ALTER TABLE data_stockpile.{name}_data OWNER TO data_stockpile;
        GRANT SELECT ON TABLE data_stockpile.{name}_data TO all_read_only;
        
        -- rawdata error table
        CREATE TABLE data_stockpile.{name}_rawdata_error (
            key varchar(64),
            log jsonb NOT NULL,
            create_date timestamp with time zone NOT NULL DEFAULT now(),
            update_date timestamp with time zone,
            CONSTRAINT {name}_rawdata_error_key_pk PRIMARY KEY (key)
        );
        ALTER TABLE data_stockpile.{name}_rawdata_error  OWNER TO data_stockpile;
        GRANT SELECT ON TABLE data_stockpile.{name}_rawdata_error TO all_read_only;
        
    """.format(name=name)

    print(query)
    stmtInit = sqlalchemy.text(query)

    try:
        with db.connect() as conn:
            conn.execute(stmtInit)
            return json.dumps({'msg': 'new campaign successfully created'}), 200, header_json
    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def update_container(container, request_json):
    """
    # update exist container (all fields except public_ref)
    # @Return json
    """

    query = '''
    Update data_stockpile.container
    SET active={active},
        update_date=NOW()
    WHERE name = '{container_id}';
    
    '''.format(**request_json, container_id=container['name'])

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            conn.execute(stmt)
            return json.dumps({'msg': 'container successfully updated'}), 200, header_json

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def exist_container(container_name):
    query = '''
        SELECT * FROM data_stockpile.container where name = '{name}'
    '''.format(name=container_name)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            result = (conn.execute(stmt)).first()
            if result:
                return True
            return False
    except Exception as e:
        print('DB Exception: {}'.format(str(e)))
        return json.dumps({'error': 'DB Exception: {e}'.format(e=str(e))}), 500, header_json


def get_container(container_name):
    """
    Return container data or False if not found for the given name
    :ivar string
    :return dict or False
    """

    query = '''
        SELECT * FROM data_stockpile.container where name = '{name}'
    '''.format(name=container_name)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            container = (conn.execute(stmt)).first()
            if container:
                logging.info('container : {}'.format(str(container._asdict())))
                return container._asdict()
            return False

    except Exception as e:
        print('DB Exception: {}'.format(str(e)))
        return json.dumps({'error': 'DB Exception: {e}'.format(e=str(e))}), 500, header_json


def get_data_by_key(key, container):
    query = '''
        SELECT *
        FROM data_stockpile.{container}_rawdata
        WHERE key = '{key}';
    '''.format(container=container,
               key=key)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            data_profile = (conn.execute(stmt)).first()
            if data_profile:
                logging.info('container : {}'.format(str(data_profile._asdict())))
                return json.dumps(data_profile._asdict(), default=str), 200, header_json
            else:
                return json.dumps({'error': 'Not found'}), 204, header_json
    except Exception as e:
        logging.info('DB Exception: {}'.format(str(e)))
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json


def push_data_by_key(key, container, data):
    query = '''
        INSERT INTO data_stockpile.{container}_rawdata(
	        key, data, create_date)
	    VALUES ('{key}', CAST('{data}' AS jsonb), NOW())
        ON CONFLICT (key) DO UPDATE
        SET data = CAST('{data}' AS jsonb),
        update_date= NOW()
        ;
    '''.format(container=container,
               key=key,
               data=data)

    stmt = sqlalchemy.text(query)
    try:
        with db.connect() as conn:
            conn.execute(stmt)
            return json.dumps({'msg': 'Success'}), 200, header_json

    except Exception as e:
        return json.dumps({'error': 'Server Error: {e}'.format(e=str(e))}), 500, header_json
