# Import flask dependencies
import json
import os
from functools import wraps

import firebase_admin
from firebase_admin import auth
from flask import Blueprint, jsonify, make_response, request

import app.data_enrich.container_service as cs

FIREBASE_PROJECT_ID = os.getenv('FIREBASE_PROJECT_ID')
firebase_admin.initialize_app(options={
    "projectId": FIREBASE_PROJECT_ID
})

cs.init_db()
header_json = {'content-type': 'application/json'}
global_x_qualifio_auth = '64e0436b2fb96672092e81bba7159a6bf44902bf30c74e4b738e878b4b39674c'


# Define the blueprint: 'auth', set its url prefix: app.url/auth

def authenticated(fn):
    @wraps(fn)
    def wrapped():
        try:
            # Extract the firebase token from the HTTP header
            token = request.headers['Authorization']
            token = token.replace('Bearer ', '')
            # Validate the token
            verified = auth.verify_id_token(token)
        except Exception as e:
            # If an exception occured above, reject the request
            msg = 'Invalid Credentials:{}'.format(e)
            resp = make_response(jsonify(message=msg), 401)
            return resp
        # Execute the authenticated function
        return fn(request)
        # Return the input function "wrapped" with our
        # authentication check, i.e. fn(authenticated(request))

    return wrapped


# Define the blueprint: 'auth', set its url prefix: app.url/auth
enrich_get_blueprint = Blueprint('data_enrich_get', __name__, url_prefix='/data-enrich-get')


# Set the route and accepted methods

@enrich_get_blueprint.route('/', methods=['GET'])
@authenticated
def data_enrich_get(request):
    request_arguments = request.args
    if 'container' not in request_arguments:
        return json.dumps({'error': 'container argument is undefined'}), 400, header_json

    # validate key
    if 'key' not in request_arguments:
        return json.dumps({'error': 'key argument is undefined'}), 400, header_json

    if len(request_arguments.get('key')) > 64:
        return json.dumps({'error': 'invalid argument: length key value is longer than the maximum authorized (64)'}), \
               400, header_json

    key = (request.args.get('key')).strip()
    container = (request.args.get('container')).strip()

    is_valid_container = cs.exist_container(container)
    if not is_valid_container:
        return json.dumps({'error': 'container is disabled or not found in our DB'}), 404, header_json

    return cs.get_data_by_key(key, container)


enrich_push_blueprint = Blueprint('data_enrich_push', __name__, url_prefix='/data-enrich-push')


@enrich_push_blueprint.route('/', methods=['POST'])
@authenticated
def data_enrich_push(request):
    request_json = request.get_json()
    if request_json is None:
        return json.dumps({'error': 'no input provided'}), 400, header_json

    print('stockpile raw data : {}'.format(json.dumps(request_json)))

    if 'container' not in request_json:
        return json.dumps({'error': 'container argument is undefined'}), 400, header_json

    if 'data' not in request_json:
        return json.dumps({'error': 'data argument is undefined'}), 400, header_json

    # valid key
    if 'key' not in request_json or len(request_json['key']) > 64:
        return json.dumps({'error': 'key is invalid or undefined'}), 400, header_json

    is_valid_container = cs.exist_container(request_json['container'])
    if not is_valid_container:
        return json.dumps({'error': 'container is disabled or not found in our DB'}), 404, header_json

    format_request = str(json.dumps(request_json['data']).replace("'", '\\"'))
    print('stockpile raw data format : {}'.format(format_request))

    return cs.push_data_by_key(key=request_json['key'],
                               container=request_json['container'],
                               data=format_request)


container_manager_blueprint = Blueprint('container_manager', __name__, url_prefix='/data-enrich/container-manager')


@container_manager_blueprint.route('/', methods=['GET', 'POST', 'PUT'])
@authenticated
def container_manager(request):
    # GET = list containers , POST = create container, PUT = update container
    if request.method == 'GET':
        request_arguments = request.args
        if 'container' not in request_arguments:
            return cs.list_container()
        else:
            container_name = (request_arguments.get('container')).strip()
            print(container_name)
            container_info = cs.get_container(container_name)
            if container_info:
                return json.dumps(container_info, default=str), 200, header_json
            return json.dumps({'error': 'container was not found in our DB'}), 404, header_json

    elif request.method == 'POST' or request.method == 'PUT':
        request_json = request.get_json()

        if request_json is None:
            return json.dumps({'error': 'Invalid request'}), 400, header_json

        binding_key = ['name']
        for key in binding_key:
            if key not in request_json:
                return json.dumps({'error': '{key} argument is undefined'.format(key=str(key))}), 400, header_json

        if 'active' not in request_json:
            request_json['active'] = True
        request_json['active'] = bool(request_json['active'])

        container = cs.get_container(request_json['name'])
        if request.method == 'PUT':
            if not container:
                print('[data_enrich container] container was not found in our DB !! : {}'
                      .format(str(request_json['name'])))
                return json.dumps(
                    {'error': '[data_enrich container] container was not found in our DB !!'}), 404, header_json
            return cs.update_container(container, request_json)
        else:
            if container:
                return json.dumps({'error': 'container name already used'}), 400, header_json
            return cs.create_container(request_json)

