import logging
import os

import sqlalchemy
from dns.e164 import query
from sqlalchemy import create_engine

import secret_manager as secret

logging.basicConfig()  # log messages to stdout
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(logging.INFO)


def init_db():
    """
    # init database
    # @Return sqlalchemy engine
    """
    env = os.environ.get("ENV")

    db_port = os.environ.get("DB_PORT", 5432)
    db_user = os.environ.get("DB_USERNAME_STOCKPILE", 'data_stockpile_app')
    db_pass = secret.get_secret(os.environ["PROJECT_ID"], os.environ["SECRET_STOCKPILE_PWD_ID"])
    # db_pass = os.environ["SECRET_STOCKPILE_PWD_ID"]
    db_name = os.environ.get("DB_DATABASE", "matrix")
    db_socket_dir = os.environ.get("DB_SOCKET_DIR", "/cloudsql")
    # i.e "<PROJECT-NAME>:<INSTANCE-REGION>:<INSTANCE-NAME>"
    cloud_sql_connection_name = os.environ["DB_CONNEXION_STRING"]

    if env == 'dev':

        #  init tcp connection engine for dev env using cloud sql proxy
        engine_url = sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=db_user,  # e.g. "my-database-user"
            password=db_pass,  # e.g. "my-database-password"
            host=cloud_sql_connection_name,  # e.g. "127.0.0.1"
            port=db_port,  # e.g. 5432
            database=db_name,  # e.g. "my-database-name"
            query={"sslmode": "disable"}
        )

    else:
        # init unix connection engine
        engine_url = sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=db_user,
            password=db_pass,
            database=db_name,
            host=cloud_sql_connection_name,  # e.g. "127.0.0.1"
            port=db_port,  # e.g. 5432
            query={
                "unix_sock": "{}/{}/.s.PGSQL.5432".format(
                    db_socket_dir,
                    cloud_sql_connection_name)
            }
        )



    db = sqlalchemy.create_engine(
        engine_url,
        pool_size=5,
        max_overflow=2,
        pool_timeout=30,
        pool_recycle=1800,
        # executemany_mode = 'values_plus_batch',
        client_encoding="utf8",
        execution_options={
            "isolation_level": "AUTOCOMMIT"
        }
    )

    return db
