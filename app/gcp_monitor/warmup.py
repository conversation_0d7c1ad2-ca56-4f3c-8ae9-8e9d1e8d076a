import json

from flask import Blueprint, request

warmup_blueprint = Blueprint('warmup', __name__, url_prefix='/_ah/warmup')

header_json = {'content-type': 'application/json'}


@warmup_blueprint.route('/', methods=['GET'])
def warmup_main():
    """
        Used to deal with warmup GCP calls
        Args:

        Returns:

    """
    request_json = request.get_json(force=True, silent=True)

    return json.dumps({'response': 'success warmup script'}), 200, header_json
