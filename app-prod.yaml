runtime: python311

entrypoint: gunicorn -t 120 -b :$PORT run:app

instance_class: F4
resources:
  memory_gb: 3

inbound_services:
  - warmup

automatic_scaling:
  min_instances: 1
  min_idle_instances: 1
  max_idle_instances: 1
  min_pending_latency: 800ms
  max_pending_latency: 1500ms
  target_cpu_utilization: 0.7
  target_throughput_utilization: 0.8
  max_concurrent_requests: 100


env_variables:
  ENV: prod
  PROJECT_ID: pm-prod-data-api
  DB_DATABASE: matrix
  DB_CONNEXION_STRING: pm-prod-matrix:europe-west1:matrix-db-9-6
  DB_USERNAME_STOCKPILE: data_stockpile_app
  DB_USERNAME_ESAMPLING: esampling_app
  #SECRET_ESAMPLING_PWD: matrix_esampling_app_pwd
  SECRET_ESAMPLING_PWD_ID: matrix_esampling_app_pwd
  SECRET_STOCKPILE_PWD_ID: matrix_data_stockpile_app_pwd
  SECRET_MAGAZINE_REPORT_SMTP_PWD:  report_magazin_smtp_pass
  FIREBASE_API_KEY_SECRET: firebase_api_data_key
  DB_USERNAME_ROGUEONE: rogue_one_app
  SECRET_ROGUEONE_PWD_ID: rogue_one_app_pwd
  PMC_EMAIL_TOKEN_SECRET: pmc_email_token_secret
  WEBRIVAGE_EMAIL_TOKEN_SECRET: webrivage_email_token_secret
  SECRET_BMW_DRIFTROCK_API_KEY: bmw_driftrock_api_key
  DD_HOST: https://data-direct.prismadata.fr
  TMAIL_HOST: https://tmail.prismadata.fr
  SAFE_PROJECT_ID: "pm-prod-safe"