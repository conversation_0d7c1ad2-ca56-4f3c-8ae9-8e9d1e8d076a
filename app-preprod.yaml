runtime: python311

entrypoint: gunicorn -b :$PORT run:app

instance_class: F4
resources:
  memory_gb: 3

automatic_scaling:
  min_idle_instances: automatic
  max_idle_instances: automatic
  min_pending_latency: 2000ms
  max_pending_latency: automatic
  target_cpu_utilization: 0.7
  min_instances: 0
  max_instances: 5
  target_throughput_utilization: 0.6
  max_concurrent_requests: 80

env_variables:
  ENV: preprod
  PROJECT_ID: pm-preprod-data-api
  DB_DATABASE: matrix
  DB_CONNEXION_STRING: pm-preprod-matrix:europe-west1:preprod-matrix-db-9-6
  DB_USERNAME_STOCKPILE: data_stockpile_app
  DB_USERNAME_ESAMPLING: esampling_app
  #SECRET_ESAMPLING_PWD: matrix_esampling_app_pwd
  SECRET_ESAMPLING_PWD_ID: matrix_esampling_app_pwd
  SECRET_STOCKPILE_PWD_ID: matrix_data_stockpile_app_pwd
  SECRET_MAGAZINE_REPORT_SMTP_PWD:  report_magazin_smtp_pass
  FIREBASE_API_KEY_SECRET: firebase_api_data_key
  DB_USERNAME_ROGUEONE: rogue_one_app
  SECRET_ROGUEONE_PWD_ID: rogue_one_app_pwd
  PMC_EMAIL_TOKEN_SECRET: pmc_email_token_secret
  WEBRIVAGE_EMAIL_TOKEN_SECRET: webrivage_email_token_secret
  SECRET_BMW_DRIFTROCK_API_KEY: bmw_driftrock_api_key
  DD_HOST: https://data-direct.preprod.prismadata.fr
  TMAIL_HOST: https://tmail.preprod.prismadata.fr
  SAFE_PROJECT_ID: "pm-preprod-safe"
  #FIREBASE_PROJECT_ID: pm-preprod-mozart-eu


  #gcloud app deploy app-preprod.yaml --project=pm-preprod-data-api --no-promote