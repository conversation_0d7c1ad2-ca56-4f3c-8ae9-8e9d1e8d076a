# Data Api

data-api project

# Utilisation

Supported functions:

* get token from firebase using email / pwd
* data-enrich api (get / push)
* manage esampling campaign (create, update, get)
* get optins from Qualifio Webhook V2

# Uri

* prod: http://data-api.prismadata.fr/
* preprod: http://data-api.preprod.prismadata.fr/

# Setup

## Dev Environment

Inside data-api/ project folder:

1. create a python3 virtual environment. Activate it and install the required dependencies to this project.

   `$ python3 -m venv venv`
   
   `$ . venv/bin/activate`
   
   `$ pip install -U pip`
   
   `$ pip install -r requirements.txt`
   
2. Install google-cloud packages:
   `
   pip install google-cloud
   `

3. dev_appserver requires Python 2.7.12+

   OSX users will need to run:

   `
   brew install pr0d1r2/python2/python@2.7.17 --build-from-source
   `

   OR

   `
   sudo apt-get install python2 python2-dev
   `

4. To use Cloud Sql proxy, enable the proxy using this command for preprod server connection:

   `
   ./cloud_sql_proxy -instances=pm-preprod-matrix:europe-west1:preprod-matrix-db-9-6=tcp:65000
   `

   you can change connection information in app.yaml file.

5. Configure your Postman environment to use http://0.0.0.0:8080 as host.

6. To execute app in dev environment execute this command in data-api project folder:

   `
   dev_appserver.py app.yaml --application=pm-preprod-data-api
   `
   
7. For more info see https://cloud.google.com/appengine/docs/standard/python3/tools/local-devserver-command?hl=fr

## Create new data-api function

- Create folder in app/{function_name_folder}
- Add file  app/{function_name_folder}/__init.py 
- Add file app/{function_name_folder}/controllers.py 
- In controllers.py define your function then add blueprint route
  
   `

      // IN app/your_function/controllers.py
  
      name_blueprint = Blueprint('to_audika', __name__, 
                              url_prefix='/qualifio-to-audika')
  
      @name_blueprint.route('/', methods=['GET', 'POST', 'PUT'])
  
      def function_name():
  
      // your code 
     
   `

- Support new blueprint route in the data-api dispatcher
  
   `
  
      // IN app/__init.py
      // declarer le blueprint ajouté
      // voir exemple webhook_blueprint
  
      from app.qualifio_webhook_v2.controllers import webhook_blueprint
      
      // then
      app.register_blueprint(webhook_blueprint)

   `

