from app import app

#if __name__ == '__main__':
# This is used when running locally only. When deploying to Google App
# Engine, a webserver process such as <PERSON><PERSON> will serve the app. This
# can be configured by adding an `entrypoint` to app.yaml.
#    app.run(host='127.0.0.1', port=8080, debug=True)
# [END gae_python3_app]
# [END gae_python38_app]

# app.run(host='127.0.0.1', port=8085, debug=True)
