runtime: python312

entrypoint: gunicorn -b :$PORT run:app --timeout 120

automatic_scaling:
  min_idle_instances: automatic
  max_idle_instances: automatic
  min_pending_latency: 2000ms
  max_pending_latency: automatic
  target_cpu_utilization: 0.7
  min_instances: 0
  max_instances: 5
  target_throughput_utilization: 0.6
  max_concurrent_requests: 80

env_variables:
  ENV: "dev"
  PROJECT_ID: pm-preprod-data-api
  DB_DATABASE: matrix
  DB_USERNAME_STOCKPILE: data_stockpile_app
  DB_USERNAME_ESAMPLING: esampling_app
  #SECRET_ESAMPLING_PWD: matrix_esampling_app_pwd
  SECRET_ESAMPLING_PWD_ID: matrix_esampling_app_pwd
  SECRET_STOCKPILE_PWD_ID: matrix_data_stockpile_app_pwd
  SECRET_MAGAZINE_REPORT_SMTP_PWD: report_magazin_smtp_pass
  FIREBASE_API_KEY_SECRET: firebase_api_data_key
  PMC_EMAIL_TOKEN_SECRET: pmc_email_token_secret
  WEBRIVAGE_EMAIL_TOKEN_SECRET: webrivage_email_token_secret
  #FIREBASE_PROJECT_ID: pm-preprod-mozart-eu
  DB_CONNEXION_STRING: 127.0.0.1
  DB_PORT: 5433
  FIREBASE_PROJECT_ID: "pm-preprod-data-api"
  SAFE_PROJECT_ID: "pm-preprod-safe"
  GOOGLE_APPLICATION_CREDENTIALS: "/Users/<USER>/.config/gcloud/application_default_credentials.json"
  SECRET_BMW_DRIFTROCK_API_KEY: bmw_driftrock_api_key
  DD_HOST: https://data-direct.preprod.prismadata.fr
  TMAIL_HOST: https://tmail.preprod.prismadata.fr
# use command : > dev_appserver.py app.yaml --application=pm-preprod-data-api
# dev_appserver.py app.yaml --application=pm-preprod-data-api --log_level=debug --dev_appserver_log_level=debug --support_datastore_emulator=false
# to run dev env in data-api folder
